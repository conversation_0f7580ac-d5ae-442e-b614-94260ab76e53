class CreateAddresses < ActiveRecord::Migration[7.1]
  def change
    create_table :addresses do |t|
      t.string :street, null: false
      t.string :city, null: false
      t.string :province, null: false
      t.string :postal_code, null: false
      t.string :country, null: false
      t.references :addressable, polymorphic: true, null: false

      t.timestamps
    end
    add_index :addresses, [:addressable_type, :addressable_id]
  end
end
