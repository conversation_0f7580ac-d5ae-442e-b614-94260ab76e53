class AddCartAndLineItems < ActiveRecord::Migration[7.1]
  def change
    create_table :carts do |t|
      t.references :user, foreign_key: true, null: true
      t.timestamps
    end

    create_table :line_items do |t|
      t.references :cart, foreign_key: true, null: true
      t.references :order, foreign_key: true, null: true
      t.references :product, foreign_key: true, null: false
      t.integer :quantity, null: false, default: 1
      t.decimal :price, precision: 10, scale: 2, null: false, default: 0
      t.timestamps
    end
  end
end
