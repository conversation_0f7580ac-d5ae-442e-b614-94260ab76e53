class CreateProductCountryData < ActiveRecord::Migration[7.1]
  def change
    create_table :product_country_data do |t|
      t.references :product, null: false, foreign_key: true
      t.string :country, null: false
      t.decimal :msrp, precision: 10, scale: 2
      t.integer :points_earned
      t.integer :points_cost

      t.timestamps
    end
    add_index :product_country_data, [:product_id, :country], unique: true
  end
end
