class CreateSales < ActiveRecord::Migration[7.1]
  def change
    create_table :sales do |t|
      t.string :serial_number, null: false
      t.references :user, null: false, foreign_key: true
      t.references :product, null: false, foreign_key: true
      t.integer :status, null: false, default: 0
      t.datetime :sold_at, null: false
      t.integer :points, null: false
      t.text :notes
      t.datetime :approved_at
      t.integer :approved_by

      t.timestamps
    end
    add_index :sales, :serial_number, unique: true
    add_index :sales, :approved_by
  end
end
