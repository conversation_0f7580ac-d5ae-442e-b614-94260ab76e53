class ChangeProductCountryDataCountryToReference < ActiveRecord::Migration[7.1]
  def change
    add_reference :product_country_data, :country, null: false, foreign_key: true
    reversible do |dir|
      dir.up do
        # Migrate existing data: match by code
        ProductCountryDatum.reset_column_information
        ProductCountryDatum.find_each do |pcd|
          country = Country.find_by(code: pcd.country)
          pcd.update!(country_id: country&.id)
        end
      end
    end
    remove_column :product_country_data, :country, :string
    add_index :product_country_data, [:product_id, :country_id], unique: true
  end
end
