class CreateOrders < ActiveRecord::Migration[7.1]
  def change
    create_table :orders do |t|
      t.references :user, null: false, foreign_key: true
      t.references :product, null: false, foreign_key: true
      t.integer :status, default: 0, null: false
      t.string :shipping_type, null: false # 'user' or 'store'
      t.string :shipping_address
      t.integer :points, null: false
      t.timestamps
    end
  end
end
