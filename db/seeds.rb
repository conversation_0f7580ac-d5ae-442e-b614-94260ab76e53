# This file should ensure the existence of records required to run the application in every environment (production,
# development, test). The code here should be idempotent so that it can be executed at any point in every environment.
# The data can then be loaded with the bin/rails db:seed command (or created alongside the database with db:setup).
#
# Example:
#
#   ["Action", "Comedy", "Drama", "Horror"].each do |genre_name|
#     MovieGenre.find_or_create_by!(name: genre_name)
#   end

# Seed a default admin user for region association (if not present)
admin_user = User.find_or_create_by!(email: "<EMAIL>") do |user|
  user.password = "password123"
  user.password_confirmation = "password123"
  user.confirmed_at = Time.current
  user.role = :admin
  user.status = :active
  user.store_id = Store.first&.id || Store.create!(name: "Seed Store", phone_number: "1234567890").id
end

# Seed only CA and US countries
us = Country.find_or_create_by!(code: "US", name: "United States")
ca = Country.find_or_create_by!(code: "CA", name: "Canada")

# Seed a region for the states/provinces
region = Region.find_or_create_by!(name: "Seed Region") { |r| r.admin_user = admin_user }

# US states
us_states = %w[
  Alabama Alaska Arizona Arkansas California Colorado Connecticut Delaware Florida Georgia Hawaii Idaho Illinois Indiana Iowa Kansas Kentucky Louisiana Maine Maryland Massachusetts Michigan Minnesota Mississippi Missouri Montana Nebraska Nevada New_Hampshire New_Jersey New_Mexico New_York North_Carolina North_Dakota Ohio Oklahoma Oregon Pennsylvania Rhode_Island South_Carolina South_Dakota Tennessee Texas Utah Vermont Virginia Washington West_Virginia Wisconsin Wyoming
]
us_states.each do |state_name|
  State.find_or_create_by!(name: state_name.tr("_", " "), region: region)
end

# Canadian provinces and territories
ca_provinces = [
  "Alberta", "British Columbia", "Manitoba", "New Brunswick", "Newfoundland and Labrador",
  "Nova Scotia", "Ontario", "Prince Edward Island", "Quebec", "Saskatchewan",
  "Northwest Territories", "Nunavut", "Yukon"
]
ca_provinces.each do |province_name|
  State.find_or_create_by!(name: province_name, region: region)
end

# Seed initial brands
photo_brand = Brand.find_or_create_by!(name: "Photo")
optics_brand = Brand.find_or_create_by!(name: "Sports Optics")

# --- Test Product Seeds ---
puts "Seeding test products..."

camera_category = Category.find_or_create_by!(name: "Cameras", brand: photo_brand)
binoculars_category = Category.find_or_create_by!(name: "Binoculars", brand: optics_brand)

products = [
  {
    name: "Test Camera",
    sku: "CAM-1001",
    upc: "4006381333931",
    status: "active",
    description: "A test digital camera for seeding.",
    category: camera_category,
    country_data: [
      {country: us, msrp: 499.99, points_cost: 5000, points_earned: 500},
      {country: ca, msrp: 549.99, points_cost: 5500, points_earned: 550}
    ]
  },
  {
    name: "Test Binoculars",
    sku: "BIN-2002",
    upc: "4006381333948",
    status: "active",
    description: "A test pair of binoculars for seeding.",
    category: binoculars_category,
    country_data: [
      {country: us, msrp: 299.99, points_cost: 3000, points_earned: 300},
      {country: ca, msrp: 329.99, points_cost: 3300, points_earned: 330}
    ]
  },
  {
    name: "Test Compact Camera",
    sku: "CAM-1002",
    upc: "4006381333955",
    status: "active",
    description: "A compact camera for testing.",
    category: camera_category,
    country_data: [
      {country: us, msrp: 199.99, points_cost: 2000, points_earned: 200},
      {country: ca, msrp: 219.99, points_cost: 2200, points_earned: 220}
    ]
  }
]

products.each do |attrs|
  product = Product.find_or_create_by!(sku: attrs[:sku]) do |p|
    p.name = attrs[:name]
    p.upc = attrs[:upc]
    p.status = attrs[:status]
    p.description = attrs[:description]
    p.category = attrs[:category]
  end
  attrs[:country_data].each do |cd|
    ProductCountryDatum.find_or_create_by!(product: product, country: cd[:country]) do |pcd|
      pcd.msrp = cd[:msrp]
      pcd.points_cost = cd[:points_cost]
      pcd.points_earned = cd[:points_earned]
    end
  end
end

puts "Test products seeded."
