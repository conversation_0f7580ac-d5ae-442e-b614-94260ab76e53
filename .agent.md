# Rovodev Instructions

You are an expert in Ruby on Rails, PostgreSQL, Hotwire (Turbo and Stimulus), and Tailwind CSS.

Code Style and Structure

- Write concise, idiomatic Ruby code with accurate examples.
- Follow Rails conventions and best practices.
- Use object-oriented and functional programming patterns as appropriate.
- Prefer iteration and modularization over code duplication.
- Use descriptive variable and method names (e.g., user_signed_in?, calculate_total).
- Structure files according to Rails conventions (MVC, concerns, helpers, etc.).

Naming Conventions

- Use snake_case for file names, method names, and variables.
- Use CamelCase for class and module names.
- Follow Rails naming conventions for models, controllers, and views.

Ruby and Rails Usage

- Use Ruby 3.x features when appropriate (e.g., pattern matching, endless methods).
- Leverage Rails' built-in helpers and methods.
- Use ActiveRecord effectively for database operations.

Syntax and Formatting

- Follow the Ruby Style Guide (<https://rubystyle.guide/>)
- Use Ruby's expressive syntax (e.g., unless, ||=, &.)
- Prefer single quotes for strings unless interpolation is needed.
- Enums are defined like `enum :status, { active: 0, inactive: 1 }`

Error Handling and Validation

- Use exceptions for exceptional cases, not for control flow.
- Implement proper error logging and user-friendly messages.
- Use ActiveModel validations in models.
- Handle errors gracefully in controllers and display appropriate flash messages.

UI and Styling

- Use Hotwire (Turbo and Stimulus) for dynamic, SPA-like interactions.
- Implement responsive design with Tailwind CSS.
- Use Rails view helpers and partials to keep views DRY.
- Use Zeiss colors where appropriate.
- Mobile-first design, focusing on PWA.

Performance Optimization

- Use database indexing effectively.
- Implement caching strategies (fragment caching, Russian Doll caching).
- Use eager loading to avoid N+1 queries.
- Optimize database queries using includes, joins, or select.

Key Conventions

- Follow RESTful routing conventions.
- Use concerns for shared behavior across models or controllers.
- Implement service objects for complex business logic.
- Use background jobs (e.g., Sidekiq) for time-consuming tasks.

## Testing with RSpec

- Use [RSpec](https://rspec.info/) for all tests. Organize specs in `spec/` by type: `models/`, `controllers/`, `requests/`, etc.
- Use [FactoryBot](https://github.com/thoughtbot/factory_bot) for factories, and [Faker](https://github.com/faker-ruby/faker) for generating test data. Place factories in `spec/factories/`.
- Run tests with `bundle exec rspec` or `bin/rspec` if available.
- Prefer feature specs for end-to-end flows, request specs for API endpoints, and system specs for Hotwire/Turbo interactions.
- Use `rails_helper.rb` for Rails config and `spec_helper.rb` for general config.
- Use `let`, `subject`, and shared examples for DRY, expressive specs.
- Use `before(:each)`/`before(:all)` for setup, and `after` hooks for teardown.
- Mock external services and background jobs using RSpec's mocking/stubbing features.

## Modern Rails Project Practices

- Use idiomatic, concise Ruby and Rails 8 features (pattern matching, endless methods, etc.).
- Prefer modular, maintainable code: use concerns, service objects, and helpers.
- Use Hotwire (Turbo/Stimulus) for SPA-like UX; place controllers in `app/javascript/controllers/`.
- Tailwind CSS is imported via `app/assets/tailwind/application.css` and built with Foreman (`bin/dev`).
- Use Importmap for JS dependencies (`config/importmap.rb`).
- Use Propshaft for asset pipeline.
- Use SolidQueue for background jobs and SolidCache for caching (see `config/environments/production.rb`).
- Use Kamal for deployment and Docker for production builds.
- Lint Ruby with `bin/rubocop`, check security with `bin/brakeman`, and audit JS with `bin/importmap audit`.

## Security

- Use strong parameters in controllers.
- Use built-in Rails protections for CSRF, XSS, and SQL injection.
- Add authentication/authorization (e.g., Devise, Pundit) as needed.

## Notable Files & Directories

- `Procfile.dev`, `bin/dev`, `bin/setup`, `config/importmap.rb`, `app/javascript/controllers/`, `app/assets/tailwind/`, `app/views/pwa/`, `.github/workflows/ci.yml`.

Follow the official Ruby on Rails guides for best practices in routing, controllers, models, views, and other Rails components.
