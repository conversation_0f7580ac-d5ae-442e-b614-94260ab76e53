# Omakase Ruby styling for Rails
inherit_gem: { rubocop-rails-omakase: rubocop.yml }

# Overwrite or add rules to create your own house style
#
# # Use `[a, [b, c]]` not `[ a, [ b, c ] ]`
# Layout/SpaceInsideArrayLiteralBrackets:
#   Enabled: false
Layout/SpaceInsideArrayLiteralBrackets:
  Enabled: false

Layout/SpaceInsideHashLiteralBraces:
  Enabled: false

AllCops:
  Exclude:
    - "vendor/**/*"
    - "db/schema.rb"
    - "bin/*"
    - "node_modules/**/*"
    - "tmp/**/*"
    - "/usr/lib/**"
    - "/usr/local/lib/**"
    - "/lib/**"
    - "/opt/**"
    - "bundle/**"
