require "rails_helper"

RSpec.describe "Enhanced Admin Functionality", type: :request do
  let(:admin) { create(:user, role: :admin, status: :active, store: create(:store, :active)) }
  let(:super_admin) { create(:user, role: :super_admin, status: :active, store: create(:store, :active)) }
  let(:regular_user) { create(:user, role: :regular, status: :active, store: create(:store, :active)) }

  describe "Admin Base Controller" do
    context "with admin user" do
      before { login_as(admin, scope: :user) }

      it "allows access to admin dashboard" do
        get admin_root_path
        expect(response).to have_http_status(:success)
      end

      it "includes Pagy backend for pagination" do
        expect(Admin::BaseController.included_modules).to include(Pagy::Backend)
      end
    end

    context "with regular user" do
      before { login_as(regular_user, scope: :user) }

      it "denies access to admin areas" do
        get admin_root_path
        expect(response).to redirect_to(root_path)
        expect(flash[:alert]).to eq("Access denied. Admin privileges required.")
      end
    end

    context "with unauthenticated user" do
      it "redirects to sign in" do
        get admin_root_path
        expect(response).to redirect_to(new_user_session_path)
      end
    end
  end

  describe "Enhanced Sales Management" do
    let(:user) { create(:user) }
    let(:product) { create(:product) }
    let!(:pending_sale) { create(:sale, user: user, product: product, status: :pending, sold_at: 1.day.ago) }
    let!(:approved_sale) { create(:sale, user: user, product: product, status: :approved, sold_at: 2.days.ago) }

    before { login_as(admin, scope: :user) }

    it "lists sales with pagination" do
      get admin_sales_path
      expect(response).to have_http_status(:success)
      expect(assigns(:pagy)).to be_present
      expect(assigns(:sales)).to include(pending_sale, approved_sale)
    end

    it "filters sales by status" do
      get admin_sales_path, params: {q: {status_eq: "pending"}}
      expect(response).to have_http_status(:success)
      expect(assigns(:sales)).to include(pending_sale)
      expect(assigns(:sales)).not_to include(approved_sale)
    end

    it "searches sales by user email" do
      get admin_sales_path, params: {q: {user_email_cont: user.email.split("@").first}}
      expect(response).to have_http_status(:success)
      expect(assigns(:sales)).to include(pending_sale, approved_sale)
    end

    it "searches sales by product name" do
      get admin_sales_path, params: {q: {product_name_cont: product.name}}
      expect(response).to have_http_status(:success)
      expect(assigns(:sales)).to include(pending_sale, approved_sale)
    end

    it "searches sales by serial number" do
      get admin_sales_path, params: {q: {serial_number_cont: pending_sale.serial_number}}
      expect(response).to have_http_status(:success)
      expect(assigns(:sales)).to include(pending_sale)
    end

    it "approves pending sales" do
      expect {
        post approve_admin_sale_path(pending_sale)
      }.to change { pending_sale.reload.status }.from("pending").to("approved")

      expect(response).to redirect_to(admin_sales_path)
      expect(flash[:notice]).to include("approved")
    end

    it "rejects pending sales" do
      expect {
        post reject_admin_sale_path(pending_sale)
      }.to change { pending_sale.reload.status }.from("pending").to("rejected")

      expect(response).to redirect_to(admin_sales_path)
      expect(flash[:notice]).to include("rejected")
    end
  end

  describe "Enhanced User Management" do
    let!(:active_user) { create(:user, status: :active) }
    let!(:inactive_user) { create(:user, status: :inactive) }

    before { login_as(admin, scope: :user) }

    it "lists users with pagination" do
      get admin_users_path
      expect(response).to have_http_status(:success)
      expect(assigns(:pagy)).to be_present
      expect(assigns(:users)).to include(active_user, inactive_user)
    end

    it "filters users by status" do
      get admin_users_path, params: {q: {status_eq: "active"}}
      expect(response).to have_http_status(:success)
      expect(assigns(:users)).to include(active_user)
      expect(assigns(:users)).not_to include(inactive_user)
    end

    it "searches users by email" do
      get admin_users_path, params: {q: {email_cont: active_user.email.split("@").first}}
      expect(response).to have_http_status(:success)
      expect(assigns(:users)).to include(active_user)
    end

    it "filters users by role" do
      Wallet.delete_all
      User.delete_all
      store = create(:store, :active)
      admin_user = create(:user, role: :admin, email: "<EMAIL>", status: :active, confirmed_at: Time.now, store: store)
      login_as(admin_user, scope: :user)
      get admin_users_path, params: {q: {role_eq: 1}}
      expect(response).to have_http_status(:success)
      emails = assigns(:users).map(&:email)
      expect(emails).to include("<EMAIL>")
    end

    it "shows user details" do
      get admin_user_path(active_user)
      expect(response).to have_http_status(:success)
      expect(assigns(:user)).to eq(active_user)
    end

    it "updates user information" do
      Wallet.delete_all
      User.delete_all
      store = create(:store, :active)
      admin = create(:user, role: :admin, email: "<EMAIL>", status: :active, confirmed_at: Time.now, store: store)
      user = create(:user, status: :active, email: "<EMAIL>", confirmed_at: Time.now, store: store)
      login_as admin, scope: :user
      patch admin_user_path(user), params: {
        user: {email: "<EMAIL>", role: "admin", status: "active", store_id: store.id}
      }
      follow_redirect!
      user.reload
      # Email changes require confirmation, so check unconfirmed_email instead
      expect(user.unconfirmed_email).to eq("<EMAIL>")
      expect(user.role).to eq("admin")
    end

    it "activates inactive users" do
      expect {
        post activate_admin_user_path(inactive_user)
      }.to change { inactive_user.reload.status }.from("inactive").to("active")

      expect(response).to redirect_to(admin_users_path)
      expect(flash[:notice]).to include("activated")
    end

    it "deactivates active users" do
      expect {
        post deactivate_admin_user_path(active_user)
      }.to change { active_user.reload.status }.from("active").to("inactive")

      expect(response).to redirect_to(admin_users_path)
      expect(flash[:notice]).to include("deactivated")
    end
  end

  describe "Enhanced Store Management" do
    let!(:active_store) { create(:store, status: :active) }
    let!(:requested_store) { create(:store, status: :requested) }

    before { login_as(admin, scope: :user) }

    it "lists stores with pagination" do
      get admin_stores_path
      expect(response).to have_http_status(:success)
      expect(assigns(:pagy)).to be_present
      expect(assigns(:stores)).to include(active_store, requested_store)
    end

    it "filters stores by status" do
      get admin_stores_path, params: {q: {status_eq: "requested"}}
      expect(response).to have_http_status(:success)
      expect(assigns(:stores)).to include(requested_store)
      expect(assigns(:stores)).not_to include(active_store)
    end

    it "searches stores by name" do
      get admin_stores_path, params: {q: {name_cont: active_store.name}}
      expect(response).to have_http_status(:success)
      expect(assigns(:stores)).to include(active_store)
    end

    it "searches stores by city through address" do
      country = create(:country)
      region = create(:region)
      state = create(:state, region: region)
      address = active_store.create_address(city: "Toronto", street: "123 Main", postal_code: "M5H 2M9", country: country, state: state)
      address.save!

      get admin_stores_path, params: {q: {address_city_cont: "Toronto"}}
      expect(response).to have_http_status(:success)
      expect(assigns(:stores)).to include(active_store)
    end

    it "shows store details" do
      get admin_store_path(active_store)
      expect(response).to have_http_status(:success)
      expect(assigns(:store)).to eq(active_store)
    end

    it "updates store information" do
      patch admin_store_path(active_store), params: {
        store: {name: "Updated Store Name", status: "inactive"}
      }

      expect(response).to redirect_to(admin_store_path(active_store))
      expect(active_store.reload.name).to eq("Updated Store Name")
      expect(active_store.status).to eq("inactive")
    end

    it "approves requested stores" do
      expect {
        post approve_admin_store_path(requested_store)
      }.to change { requested_store.reload.status }.from("requested").to("active")

      expect(response).to redirect_to(admin_stores_path)
      expect(flash[:notice]).to include("approved")
    end

    it "activates inactive stores" do
      inactive_store = create(:store, status: :inactive)

      expect {
        post activate_admin_store_path(inactive_store)
      }.to change { inactive_store.reload.status }.from("inactive").to("active")

      expect(response).to redirect_to(admin_stores_path)
      expect(flash[:notice]).to include("activated")
    end

    it "deactivates active stores" do
      expect {
        post deactivate_admin_store_path(active_store)
      }.to change { active_store.reload.status }.from("active").to("inactive")

      expect(response).to redirect_to(admin_stores_path)
      expect(flash[:notice]).to include("deactivated")
    end
  end

  describe "Admin Dashboard Analytics" do
    let(:user) { create(:user) }
    let(:product) { create(:product) }

    before do
      login_as(admin, scope: :user)
      # Create test data
      create(:sale, user: user, product: product, status: :pending, sold_at: 1.day.ago)
      create(:sale, user: user, product: product, status: :approved, points: 100, sold_at: 2.days.ago)
      create(:user, status: :active)
      create(:store, status: :active)
    end

    it "displays dashboard statistics" do
      get admin_root_path
      expect(response).to have_http_status(:success)

      expect(assigns(:pending_sales_count)).to eq(1)
      expect(assigns(:total_users)).to be >= 2 # admin + created user
      expect(assigns(:total_stores)).to be >= 1
      expect(assigns(:total_points_awarded)).to eq(100)
    end

    it "shows recent sales activity" do
      get admin_root_path
      expect(response).to have_http_status(:success)
      expect(assigns(:recent_sales)).to be_present
    end

    it "shows recent admin activity" do
      # Approve a sale to create admin activity
      sale = create(:sale, user: user, product: product, status: :pending, sold_at: 1.day.ago)
      sale.update!(status: :approved, approved_by: admin.id, approved_at: Time.current)

      get admin_root_path
      expect(response).to have_http_status(:success)
      expect(assigns(:recent_activity)).to include(sale)
    end
  end
end
