# frozen_string_literal: true

# == Schema Information
#
# Table name: addresses
#
#  id               :bigint           not null, primary key
#  addressable_type :string           not null
#  city             :string           not null
#  postal_code      :string           not null
#  street           :string           not null
#  created_at       :datetime         not null
#  updated_at       :datetime         not null
#  addressable_id   :bigint           not null
#  country_id       :bigint           not null
#  state_id         :bigint
#
# Indexes
#
#  index_addresses_on_addressable                          (addressable_type,addressable_id)
#  index_addresses_on_addressable_type_and_addressable_id  (addressable_type,addressable_id)
#  index_addresses_on_country_id                           (country_id)
#  index_addresses_on_state_id                             (state_id)
#
# Foreign Keys
#
#  fk_rails_...  (country_id => countries.id)
#  fk_rails_...  (state_id => states.id)
#
require "rails_helper"

RSpec.describe Address, type: :model do
  let(:state) { FactoryBot.create(:state) }
  let(:addressable) { FactoryBot.create(:user) }
  subject { FactoryBot.build(:address, state: state, addressable: addressable) }

  describe "validations" do
    it { is_expected.to validate_presence_of(:street) }
    it { is_expected.to validate_presence_of(:city) }
    it { is_expected.to validate_presence_of(:postal_code) }
    it { is_expected.to validate_presence_of(:country_id) }
    it { is_expected.to validate_presence_of(:state_id) }
    it { is_expected.to belong_to(:country) }
  end

  describe "associations" do
    it { is_expected.to belong_to(:addressable) }
    it { is_expected.to belong_to(:state).optional(false) }
  end
end
