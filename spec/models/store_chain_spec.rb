# frozen_string_literal: true

# == Schema Information
#
# Table name: store_chains
#
#  id         :bigint           not null, primary key
#  name       :string           not null
#  created_at :datetime         not null
#  updated_at :datetime         not null
#
# Indexes
#
#  index_store_chains_on_name  (name) UNIQUE
#
require "rails_helper"

RSpec.describe StoreChain, type: :model do
  subject(:store_chain) { build(:store_chain) }

  describe "validations" do
    it { is_expected.to validate_presence_of(:name) }
    it { is_expected.to validate_uniqueness_of(:name) }
  end

  describe "associations" do
    it { is_expected.to have_many(:stores) }
  end
end
