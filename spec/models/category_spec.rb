# frozen_string_literal: true

# == Schema Information
#
# Table name: categories
#
#  id         :bigint           not null, primary key
#  name       :string           not null
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  brand_id   :bigint           not null
#
# Indexes
#
#  index_categories_on_brand_id           (brand_id)
#  index_categories_on_name_and_brand_id  (name,brand_id) UNIQUE
#
# Foreign Keys
#
#  fk_rails_...  (brand_id => brands.id)
#
require "rails_helper"

RSpec.describe Category, type: :model do
  subject(:category) { build(:category) }

  describe "validations" do
    it { is_expected.to validate_presence_of(:name) }
    it { is_expected.to validate_uniqueness_of(:name).scoped_to(:brand_id) }
  end

  describe "associations" do
    it { is_expected.to belong_to(:brand) }
    it { is_expected.to have_many(:products).dependent(:restrict_with_exception) }
  end
end
