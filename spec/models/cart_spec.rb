# == Schema Information
#
# Table name: carts
#
#  id         :bigint           not null, primary key
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  user_id    :bigint
#
# Indexes
#
#  index_carts_on_user_id  (user_id)
#
# Foreign Keys
#
#  fk_rails_...  (user_id => users.id)
#
require "rails_helper"

RSpec.describe Cart, type: :model do
  subject(:cart) { build(:cart) }

  describe "associations" do
    it { is_expected.to belong_to(:user).optional(true) }
    it { is_expected.to have_many(:line_items).dependent(:destroy) }
    it { is_expected.to have_many(:products).through(:line_items) }
  end

  describe "validations" do
    it "is valid with valid attributes" do
      expect(cart).to be_valid
    end

    it "is valid without a user (guest cart)" do
      cart.user = nil
      expect(cart).to be_valid
    end

    it "is valid with a user" do
      cart.user = create(:user)
      expect(cart).to be_valid
    end
  end

  describe "#total_price" do
    let(:cart) { create(:cart) }
    let(:product1) { create(:product) }
    let(:product2) { create(:product) }

    context "with no line items" do
      it "returns 0" do
        expect(cart.total_price).to eq(0)
      end
    end

    context "with line items" do
      before do
        create(:line_item, cart: cart, product: product1, quantity: 2, price: 10.99)
        create(:line_item, cart: cart, product: product2, quantity: 1, price: 25.50)
      end

      it "calculates the total price correctly" do
        expected_total = (10.99 * 2) + (25.50 * 1)
        expect(cart.total_price).to eq(expected_total)
      end
    end

    context "with line items having different quantities" do
      before do
        create(:line_item, cart: cart, product: product1, quantity: 3, price: 10.99)
        create(:line_item, cart: cart, product: product2, quantity: 2, price: 25.50)
      end

      it "calculates the total price correctly" do
        expected_total = (10.99 * 3) + (25.50 * 2)
        expect(cart.total_price).to eq(expected_total)
      end
    end

    context "with zero-priced items" do
      let(:free_product) { create(:product) }

      before do
        create(:line_item, cart: cart, product: free_product, quantity: 1, price: 0)
        create(:line_item, cart: cart, product: product1, quantity: 1, price: 10.99)
      end

      it "includes free items in calculation" do
        expect(cart.total_price).to eq(10.99)
      end
    end
  end

  describe "factory" do
    it "has a valid factory" do
      expect(build(:cart)).to be_valid
    end

    it "has a valid factory with user" do
      expect(build(:cart, :with_user)).to be_valid
    end

    it "has a valid guest factory" do
      cart = build(:cart, :guest)
      expect(cart).to be_valid
      expect(cart.user).to be_nil
    end

    it "creates cart with line items using trait" do
      cart = create(:cart, :with_line_items)
      expect(cart.line_items.count).to eq(2)
      expect(cart.total_price).to be > 0
    end
  end

  describe "dependent destroy" do
    let(:cart) { create(:cart) }
    let!(:line_item) { create(:line_item, cart: cart) }

    it "destroys associated line items when cart is destroyed" do
      expect { cart.destroy }.to change(LineItem, :count).by(-1)
    end
  end
end
