# spec/models/region_spec.rb
# == Schema Information
#
# Table name: regions
#
#  id            :bigint           not null, primary key
#  name          :string           not null
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#  admin_user_id :bigint           not null
#
# Indexes
#
#  index_regions_on_admin_user_id  (admin_user_id)
#  index_regions_on_name           (name) UNIQUE
#
# Foreign Keys
#
#  fk_rails_...  (admin_user_id => users.id)
#
require "rails_helper"

RSpec.describe Region, type: :model do
  let(:admin_user) { FactoryBot.create(:user, :admin) }
  subject { FactoryBot.build(:region, admin_user: admin_user) }

  it { should belong_to(:admin_user).class_name("User") }
  it { should have_many(:states).dependent(:destroy) }
  it { should validate_presence_of(:name) }
  it { should validate_uniqueness_of(:name) }
end
