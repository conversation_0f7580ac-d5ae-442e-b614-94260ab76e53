# spec/models/state_spec.rb
# == Schema Information
#
# Table name: states
#
#  id         :bigint           not null, primary key
#  name       :string           not null
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  region_id  :bigint           not null
#
# Indexes
#
#  index_states_on_region_id           (region_id)
#  index_states_on_region_id_and_name  (region_id,name) UNIQUE
#
# Foreign Keys
#
#  fk_rails_...  (region_id => regions.id)
#
require "rails_helper"

RSpec.describe State, type: :model do
  let(:region) { FactoryBot.create(:region) }
  subject { FactoryBot.build(:state, region: region) }

  it { should belong_to(:region) }
  it { should validate_presence_of(:name) }
  it { should validate_uniqueness_of(:name).scoped_to(:region_id) }
end
