# == Schema Information
#
# Table name: line_items
#
#  id         :bigint           not null, primary key
#  price      :decimal(10, 2)   default(0.0), not null
#  quantity   :integer          default(1), not null
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  cart_id    :bigint
#  order_id   :bigint
#  product_id :bigint           not null
#
# Indexes
#
#  index_line_items_on_cart_id     (cart_id)
#  index_line_items_on_order_id    (order_id)
#  index_line_items_on_product_id  (product_id)
#
# Foreign Keys
#
#  fk_rails_...  (cart_id => carts.id)
#  fk_rails_...  (order_id => orders.id)
#  fk_rails_...  (product_id => products.id)
#
require "rails_helper"

RSpec.describe LineItem, type: :model do
  subject(:line_item) { build(:line_item) }

  describe "associations" do
    it { is_expected.to belong_to(:cart).optional(true) }
    it { is_expected.to belong_to(:order).optional(true) }
    it { is_expected.to belong_to(:product) }
  end

  describe "validations" do
    it { is_expected.to validate_numericality_of(:quantity).is_greater_than(0) }

    it "is valid with valid attributes" do
      expect(line_item).to be_valid
    end

    it "is invalid with zero quantity" do
      line_item.quantity = 0
      expect(line_item).not_to be_valid
      expect(line_item.errors[:quantity]).to be_present
    end

    it "is invalid with negative quantity" do
      line_item.quantity = -1
      expect(line_item).not_to be_valid
      expect(line_item.errors[:quantity]).to be_present
    end

    it "is valid with positive quantity" do
      line_item.quantity = 5
      expect(line_item).to be_valid
    end
  end

  describe "#total_price" do
    let(:product) { create(:product) }
    let(:line_item) { build(:line_item, product: product, quantity: 3, price: 15.99) }

    it "calculates total price correctly" do
      expect(line_item.total_price).to eq(15.99 * 3)
    end

    context "when price is different from product price" do
      let(:line_item) { build(:line_item, product: product, quantity: 2, price: 12.50) }

      it "uses the line item price, not product price" do
        expect(line_item.total_price).to eq(12.50 * 2)
      end
    end

    context "with quantity of 1" do
      let(:line_item) { build(:line_item, product: product, quantity: 1, price: 10.00) }

      it "returns the price" do
        expect(line_item.total_price).to eq(10.00)
      end
    end

    context "with zero price" do
      let(:line_item) { build(:line_item, product: product, quantity: 2, price: 0) }

      it "returns zero" do
        expect(line_item.total_price).to eq(0)
      end
    end
  end

  describe "cart vs order association" do
    let(:cart) { create(:cart) }
    let(:order) { create(:order) }

    context "when associated with cart" do
      let(:line_item) { create(:line_item, :in_cart, cart: cart) }

      it "belongs to cart and not order" do
        expect(line_item.cart).to eq(cart)
        expect(line_item.order).to be_nil
      end
    end

    context "when associated with order" do
      let(:line_item) { create(:line_item, :in_order, order: order) }

      it "belongs to order and not cart" do
        expect(line_item.order).to eq(order)
        expect(line_item.cart).to be_nil
      end
    end

    context "when moving from cart to order" do
      let(:line_item) { create(:line_item, :in_cart, cart: cart) }

      it "can be moved from cart to order" do
        line_item.update!(order: order, cart: nil)
        line_item.reload

        expect(line_item.order).to eq(order)
        expect(line_item.cart).to be_nil
      end
    end
  end

  describe "factory" do
    it "has a valid factory" do
      expect(build(:line_item)).to be_valid
    end

    it "has a valid in_cart trait" do
      line_item = build(:line_item, :in_cart)
      expect(line_item).to be_valid
      expect(line_item.cart).to be_present
      expect(line_item.order).to be_nil
    end

    it "has a valid in_order trait" do
      line_item = build(:line_item, :in_order)
      expect(line_item).to be_valid
      expect(line_item.order).to be_present
      expect(line_item.cart).to be_nil
    end

    it "sets price from product when not specified" do
      country = create(:country, code: "US", name: "United States")
      product = create(:product)
      create(:product_country_datum, product: product, country: country, msrp: 29.99)
      line_item = build(:line_item, product: product, price: 0)
      expect(line_item.price).to eq(29.99)
    end

    it "respects explicitly set price" do
      product = create(:product)
      line_item = build(:line_item, product: product, price: 19.99)
      expect(line_item.price).to eq(19.99)
    end
  end

  describe "price handling" do
    let(:country) { create(:country, code: "US", name: "United States") }
    let(:product) { create(:product) }

    before do
      create(:product_country_datum, product: product, country: country, msrp: 25.00)
    end

    context "when price is not set" do
      let(:line_item) { build(:line_item, product: product, price: 0) }

      it "uses product price" do
        expect(line_item.price).to eq(25.00)
      end
    end

    context "when price is explicitly set" do
      let(:line_item) { build(:line_item, product: product, price: 20.00) }

      it "uses the set price" do
        expect(line_item.price).to eq(20.00)
      end
    end
  end
end
