# == Schema Information
#
# Table name: wallets
#
#  id         :bigint           not null, primary key
#  points     :integer          default(0), not null
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  user_id    :bigint           not null
#
# Indexes
#
#  index_wallets_on_user_id  (user_id) UNIQUE
#
# Foreign Keys
#
#  fk_rails_...  (user_id => users.id)
#

require "rails_helper"

RSpec.describe Wallet, type: :model do
  let(:user) { create(:user) }
  subject(:wallet) { user.wallet }

  describe "#credit" do
    it "increases points by the given amount" do
      expect { wallet.credit(10) }.to change { wallet.reload.points }.by(10)
    end

    it "creates a credit activity" do
      expect {
        wallet.credit(5)
      }.to change { PublicActivity::Activity.count }.by(1)
      activity = PublicActivity::Activity.last
      expect(activity.key).to eq("wallet.credit")
      expect(activity.owner).to eq(user)
      expect(activity.trackable).to eq(wallet)
      expect(activity.parameters[:amount]).to eq(5)
    end

    it "links activity to order context if provided" do
      product = create(:product)
      order = Order.create!(user: user, product: product, points: 7, shipping_type: "standard", status: :pending)
      expect {
        wallet.credit(7, context: order)
      }.to change { PublicActivity::Activity.count }.by(1)
      activity = PublicActivity::Activity.last
      expect(activity.recipient).to eq(order)
      expect(activity.parameters[:context_type]).to eq("Order")
      expect(activity.parameters[:context_id]).to eq(order.id)
    end

    it "raises error for non-positive amount" do
      expect { wallet.credit(0) }.to raise_error(ArgumentError)
      expect { wallet.credit(-5) }.to raise_error(ArgumentError)
    end
  end

  describe "#debit" do
    before { wallet.update(points: 20) }

    it "decreases points by the given amount" do
      expect { wallet.debit(5) }.to change { wallet.reload.points }.by(-5)
    end

    it "creates a debit activity" do
      expect {
        wallet.debit(3)
      }.to change { PublicActivity::Activity.count }.by(1)
      activity = PublicActivity::Activity.last
      expect(activity.key).to eq("wallet.debit")
      expect(activity.owner).to eq(user)
      expect(activity.trackable).to eq(wallet)
      expect(activity.parameters[:amount]).to eq(3)
    end

    it "links activity to sale context if provided" do
      sale = create(:sale, user: user, points: 2, sold_at: 2.hours.ago)
      expect {
        wallet.debit(2, context: sale)
      }.to change { PublicActivity::Activity.count }.by(1)
      activity = PublicActivity::Activity.last
      expect(activity.recipient).to eq(sale)
      expect(activity.parameters[:context_type]).to eq("Sale")
      expect(activity.parameters[:context_id]).to eq(sale.id)
    end

    it "raises error for non-positive amount" do
      expect { wallet.debit(0) }.to raise_error(ArgumentError)
      expect { wallet.debit(-3) }.to raise_error(ArgumentError)
    end

    it "raises error if insufficient points" do
      expect { wallet.debit(100) }.to raise_error(StandardError, "Insufficient points")
    end
  end
end
