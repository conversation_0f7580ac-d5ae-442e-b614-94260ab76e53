# == Schema Information
#
# Table name: sales
#
#  id            :bigint           not null, primary key
#  approved_at   :datetime
#  approved_by   :integer
#  notes         :text
#  points        :integer          not null
#  serial_number :string           not null
#  sold_at       :datetime         not null
#  status        :integer          default("pending"), not null
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#  product_id    :bigint           not null
#  user_id       :bigint           not null
#
# Indexes
#
#  index_sales_on_approved_by    (approved_by)
#  index_sales_on_product_id     (product_id)
#  index_sales_on_serial_number  (serial_number) UNIQUE
#  index_sales_on_user_id        (user_id)
#
# Foreign Keys
#
#  fk_rails_...  (product_id => products.id)
#  fk_rails_...  (user_id => users.id)
#
require "rails_helper"

RSpec.describe Sale, type: :model do
  describe "associations" do
    it { should belong_to(:user) }
    it { should belong_to(:product) }
    it { should belong_to(:approved_by_admin).class_name("User").with_foreign_key("approved_by").optional }
  end

  describe "validations" do
    let(:user) { create(:user) }
    let(:product) { create(:product) }
    subject { FactoryBot.build(:sale, user: user, product: product, sold_at: 2.hours.ago) }

    it { should validate_presence_of(:serial_number) }
    it { should validate_uniqueness_of(:serial_number) }
    it { should validate_presence_of(:user) }
    it { should validate_presence_of(:product) }
    it { should validate_presence_of(:status) }
    it { should validate_presence_of(:sold_at) }
    it { should validate_presence_of(:points) }
    it { should validate_numericality_of(:points).only_integer.is_greater_than_or_equal_to(0) }

    describe "sold_at date validation" do
      it "allows today's date" do
        sale = build(:sale, user: user, product: product, sold_at: Date.current - 2.hours)
        expect(sale).to be_valid
      end

      it "allows past dates" do
        sale = build(:sale, user: user, product: product, sold_at: 1.week.ago - 2.hours)
        expect(sale).to be_valid
      end

      it "does not allow future dates" do
        sale = build(:sale, user: user, product: product, sold_at: 1.day.from_now)
        expect(sale).not_to be_valid
        expect(sale.errors[:sold_at]).to include("cannot be in the future")
      end
    end
  end

  describe "enums" do
    it { should define_enum_for(:status).with_values(pending: 0, approved: 1, rejected: 2) }
  end

  describe "factories" do
    it "has a valid factory" do
      expect(FactoryBot.build(:sale, sold_at: 2.hours.ago)).to be_valid
    end
  end
end
