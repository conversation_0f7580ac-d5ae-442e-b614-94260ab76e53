require "rails_helper"

RSpec.describe Sale, type: :model do
  let(:user) { create(:user) }
  let(:product) { create(:product) }
  let(:country) { create(:country, code: "CA") }

  before do
    user.create_address(country: country, street: "123 Main", city: "Test", postal_code: "12345")
  end

  describe "enhanced barcode validation" do
    subject { build(:sale, user: user, product: product) }

    context "with valid barcode formats" do
      it "accepts standard UPC codes" do
        sale = build(:sale, user: user, product: product, serial_number: "123456789012", sold_at: 2.hours.ago)
        expect(sale).to be_valid
      end

      it "accepts EAN-13 codes" do
        sale = build(:sale, user: user, product: product, serial_number: "1234567890123", sold_at: 2.hours.ago)
        expect(sale).to be_valid
      end

      it "accepts Code 128 alphanumeric codes" do
        sale = build(:sale, user: user, product: product, serial_number: "ABC123DEF456", sold_at: 2.hours.ago)
        expect(sale).to be_valid
      end

      it "accepts mixed alphanumeric with hyphens" do
        sale = build(:sale, user: user, product: product, serial_number: "ABC-123-DEF", sold_at: 2.hours.ago)
        expect(sale).to be_valid
      end
    end

    context "with enhanced barcode scanner integration" do
      it "handles barcodes from enhanced scanner" do
        # Simulate barcode from enhanced scanner with validation
        barcode = "ZEISS123456789"
        sale = build(:sale, user: user, product: product, serial_number: barcode, sold_at: 2.hours.ago)

        expect(sale).to be_valid
        expect(sale.serial_number).to eq(barcode)
      end

      it "validates minimum barcode length for scanner accuracy" do
        # Enhanced scanner requires minimum 8 characters for accuracy
        short_barcode = "123"
        sale = build(:sale, user: user, product: product, serial_number: short_barcode, sold_at: 2.hours.ago)

        expect(sale).not_to be_valid
        expect(sale.errors[:serial_number]).to include("is too short (minimum is 8 characters)")
      end

      it "handles maximum barcode length from scanner" do
        # Enhanced scanner supports up to 18 characters
        long_barcode = "A" * 50
        sale = build(:sale, user: user, product: product, serial_number: long_barcode, sold_at: 2.hours.ago)

        # Should still be valid as our model doesn't have max length restriction
        expect(sale).to be_valid
      end
    end

    context "with future date validation" do
      it "prevents future sale dates" do
        future_sale = build(:sale, user: user, product: product, sold_at: 1.day.from_now)
        expect(future_sale).not_to be_valid
        expect(future_sale.errors[:sold_at]).to include("cannot be in the future")
      end

      it "allows today's date" do
        today_sale = build(:sale, user: user, product: product, sold_at: Date.current)
        expect(today_sale).to be_valid
      end

      it "allows past dates" do
        past_sale = build(:sale, user: user, product: product, sold_at: 1.week.ago)
        expect(past_sale).to be_valid
      end
    end
  end

  describe "Ransack search capabilities" do
    let!(:sale1) { create(:sale, user: user, product: product, serial_number: "ABC12345", status: :pending, sold_at: 2.hours.ago) }
    let!(:sale2) { create(:sale, user: user, product: product, serial_number: "DEF45678", status: :approved, sold_at: 2.hours.ago) }

    it "searches by serial number" do
      search = Sale.ransack(serial_number_cont: "ABC")
      results = search.result

      expect(results).to include(sale1)
      expect(results).not_to include(sale2)
    end

    it "searches by status" do
      search = Sale.ransack(status_eq: "approved")
      results = search.result

      expect(results).to include(sale2)
      expect(results).not_to include(sale1)
    end

    it "searches by user email through association" do
      search = Sale.ransack(user_email_cont: user.email.split("@").first)
      results = search.result

      expect(results).to include(sale1, sale2)
    end

    it "searches by product name through association" do
      search = Sale.ransack(product_name_cont: product.name)
      results = search.result

      expect(results).to include(sale1, sale2)
    end
  end

  describe "points calculation integration" do
    let(:product_country_datum) { create(:product_country_datum, product: product, country: country, points_earned: 150) }

    before do
      product_country_datum # Ensure it exists
    end

    it "calculates points based on user's country" do
      sale = create(:sale, user: user, product: product, sold_at: 2.hours.ago, points: nil)

      # Points should be set from product country data
      expect(sale.points).to eq(150)
    end

    it "handles missing product country data gracefully" do
      # Create product without country data
      other_product = create(:product)
      sale = build(:sale, user: user, product: other_product, sold_at: 2.hours.ago)

      # Should still be valid but with 0 points
      expect(sale).to be_valid
    end
  end

  describe "admin approval workflow" do
    let(:admin) { create(:user, role: :admin) }
    let(:sale) { create(:sale, user: user, product: product, status: :pending, sold_at: 2.hours.ago) }

    it "tracks admin approval" do
      sale.update!(status: :approved, approved_by: admin.id, approved_at: Time.current)

      expect(sale.status).to eq("approved")
      expect(sale.approved_by).to eq(admin.id)
      expect(sale.approved_at).to be_present
    end

    it "allows admin search by approval status" do
      approved_sale = create(:sale, user: user, product: product, status: :approved, approved_by: admin.id, sold_at: 2.hours.ago)

      search = Sale.ransack(status_eq: "approved", approved_by_eq: admin.id)
      results = search.result

      expect(results).to include(approved_sale)
      expect(results).not_to include(sale)
    end
  end
end
