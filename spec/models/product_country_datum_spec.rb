# == Schema Information
#
# Table name: product_country_data
#
#  id            :bigint           not null, primary key
#  msrp          :decimal(10, 2)
#  points_cost   :integer
#  points_earned :integer
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#  country_id    :bigint           not null
#  product_id    :bigint           not null
#
# Indexes
#
#  index_product_country_data_on_country_id                 (country_id)
#  index_product_country_data_on_product_id                 (product_id)
#  index_product_country_data_on_product_id_and_country_id  (product_id,country_id) UNIQUE
#
# Foreign Keys
#
#  fk_rails_...  (country_id => countries.id)
#  fk_rails_...  (product_id => products.id)
#
require "rails_helper"

RSpec.describe ProductCountryDatum, type: :model do
  let(:product) { FactoryBot.create(:product) }
  let(:ca_country) { FactoryBot.create(:country, code: "CA", name: "Canada") }
  let(:us_country) { FactoryBot.create(:country, code: "US", name: "United States") }

  describe "validations" do
    it "is valid with valid attributes for CA" do
      datum = described_class.new(product: product, country: ca_country, msrp: 10.99, points_earned: 100, points_cost: 50)
      expect(datum).to be_valid
    end

    it "is valid with valid attributes for US" do
      datum = described_class.new(product: product, country: us_country, msrp: 12.99, points_earned: 120, points_cost: 60)
      expect(datum).to be_valid
    end

    it "is invalid with an unsupported country" do
      invalid_country = FactoryBot.build(:country, code: "MX", name: "Mexico")
      datum = described_class.new(product: product, country: invalid_country)
      expect(datum).not_to be_valid
      expect(datum.errors[:country_id]).to be_present
    end

    it "is invalid with negative msrp" do
      datum = described_class.new(product: product, country: ca_country, msrp: -1)
      expect(datum).not_to be_valid
      expect(datum.errors[:msrp]).to include("must be greater than or equal to 0")
    end

    it "is invalid with negative points_earned" do
      datum = described_class.new(product: product, country: ca_country, points_earned: -10)
      expect(datum).not_to be_valid
      expect(datum.errors[:points_earned]).to include("must be greater than or equal to 0")
    end

    it "is invalid with negative points_cost" do
      datum = described_class.new(product: product, country: ca_country, points_cost: -5)
      expect(datum).not_to be_valid
      expect(datum.errors[:points_cost]).to include("must be greater than or equal to 0")
    end

    it "is invalid with duplicate country for the same product" do
      described_class.create!(product: product, country: ca_country)
      duplicate = described_class.new(product: product, country: ca_country)
      expect(duplicate).not_to be_valid
      expect(duplicate.errors[:product_id]).to include("has already been taken")
    end
  end
end
