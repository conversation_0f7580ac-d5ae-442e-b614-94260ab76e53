# Factory for Wallet
# == Schema Information
#
# Table name: wallets
#
#  id         :bigint           not null, primary key
#  points     :integer          default(0), not null
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  user_id    :bigint           not null
#
# Indexes
#
#  index_wallets_on_user_id  (user_id) UNIQUE
#
# Foreign Keys
#
#  fk_rails_...  (user_id => users.id)
#
FactoryBot.define do
  factory :wallet do
    association :user, strategy: :build
    points { 0 }

    after(:build) do |wallet, evaluator|
      if wallet.user.wallet && wallet.user.wallet != wallet
        wallet.user = FactoryBot.build(:user)
      end
    end
  end
end
