# == Schema Information
#
# Table name: sales
#
#  id            :bigint           not null, primary key
#  approved_at   :datetime
#  approved_by   :integer
#  notes         :text
#  points        :integer          not null
#  serial_number :string           not null
#  sold_at       :datetime         not null
#  status        :integer          default("pending"), not null
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#  product_id    :bigint           not null
#  user_id       :bigint           not null
#
# Indexes
#
#  index_sales_on_approved_by    (approved_by)
#  index_sales_on_product_id     (product_id)
#  index_sales_on_serial_number  (serial_number) UNIQUE
#  index_sales_on_user_id        (user_id)
#
# Foreign Keys
#
#  fk_rails_...  (product_id => products.id)
#  fk_rails_...  (user_id => users.id)
#
FactoryBot.define do
  factory :sale do
    serial_number { Faker::Number.unique.hexadecimal(digits: 10) }
    association :user
    association :product
    status { :pending }
    sold_at { 2.hours.ago }
    points { 10 }
    notes { Faker::Lorem.sentence }
    approved_at { nil }
    approved_by { nil }
  end
end
