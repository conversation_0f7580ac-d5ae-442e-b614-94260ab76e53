# == Schema Information
#
# Table name: line_items
#
#  id         :bigint           not null, primary key
#  price      :decimal(10, 2)   default(0.0), not null
#  quantity   :integer          default(1), not null
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  cart_id    :bigint
#  order_id   :bigint
#  product_id :bigint           not null
#
# Indexes
#
#  index_line_items_on_cart_id     (cart_id)
#  index_line_items_on_order_id    (order_id)
#  index_line_items_on_product_id  (product_id)
#
# Foreign Keys
#
#  fk_rails_...  (cart_id => carts.id)
#  fk_rails_...  (order_id => orders.id)
#  fk_rails_...  (product_id => products.id)
#
FactoryBot.define do
  factory :line_item do
    association :product
    quantity { 1 }
    price { 10.99 }

    trait :in_cart do
      association :cart
      order { nil }
    end

    trait :in_order do
      association :order
      cart { nil }
    end

    trait :multiple_quantity do
      quantity { 3 }
    end

    trait :expensive do
      price { 99.99 }
    end

    trait :cheap do
      price { 5.99 }
    end

    # Callback to set price from product if not explicitly set
    after(:build) do |line_item|
      if line_item.price.zero? && line_item.product
        # Try to get price from product, fallback to default
        begin
          line_item.price = line_item.product.price || 10.99
        rescue
          line_item.price = 10.99
        end
      end
    end
  end
end
