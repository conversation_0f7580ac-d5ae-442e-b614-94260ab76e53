# frozen_string_literal: true

# == Schema Information
#
# Table name: addresses
#
#  id               :bigint           not null, primary key
#  addressable_type :string           not null
#  city             :string           not null
#  postal_code      :string           not null
#  street           :string           not null
#  created_at       :datetime         not null
#  updated_at       :datetime         not null
#  addressable_id   :bigint           not null
#  country_id       :bigint           not null
#  state_id         :bigint
#
# Indexes
#
#  index_addresses_on_addressable                          (addressable_type,addressable_id)
#  index_addresses_on_addressable_type_and_addressable_id  (addressable_type,addressable_id)
#  index_addresses_on_country_id                           (country_id)
#  index_addresses_on_state_id                             (state_id)
#
# Foreign Keys
#
#  fk_rails_...  (country_id => countries.id)
#  fk_rails_...  (state_id => states.id)
#
FactoryBot.define do
  factory :address do
    street { Faker::Address.street_address }
    city { Faker::Address.city }
    postal_code { Faker::Address.zip_code }
    association :country
    association :state
    addressable { nil }
  end
end
