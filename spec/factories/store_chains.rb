# frozen_string_literal: true

# == Schema Information
#
# Table name: store_chains
#
#  id         :bigint           not null, primary key
#  name       :string           not null
#  created_at :datetime         not null
#  updated_at :datetime         not null
#
# Indexes
#
#  index_store_chains_on_name  (name) UNIQUE
#
FactoryBot.define do
  factory :store_chain do
    name { Faker::Company.unique.name }
  end
end
