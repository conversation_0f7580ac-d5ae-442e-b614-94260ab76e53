# == Schema Information
#
# Table name: products
#
#  id          :bigint           not null, primary key
#  brand       :string           default("photo"), not null
#  description :text
#  name        :string           not null
#  sku         :string           not null
#  status      :string           not null
#  upc         :string           not null
#  created_at  :datetime         not null
#  updated_at  :datetime         not null
#  category_id :bigint           not null
#
# Indexes
#
#  index_products_on_category_id  (category_id)
#  index_products_on_sku          (sku) UNIQUE
#  index_products_on_upc          (upc) UNIQUE
#
# Foreign Keys
#
#  fk_rails_...  (category_id => categories.id)
#
FactoryBot.define do
  factory :product do
    name { Faker::Commerce.product_name }
    sku { Faker::Alphanumeric.unique.alphanumeric(number: 8).upcase }
    upc { Faker::Code.unique.ean } # valid unique EAN-13
    description { Faker::Lorem.sentence }
    status { "active" }
    association :category
  end
end
