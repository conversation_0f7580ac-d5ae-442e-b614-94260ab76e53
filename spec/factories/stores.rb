# frozen_string_literal: true

# == Schema Information
#
# Table name: stores
#
#  id             :bigint           not null, primary key
#  name           :string           not null
#  phone_number   :string           not null
#  status         :integer          default("requested"), not null
#  created_at     :datetime         not null
#  updated_at     :datetime         not null
#  brand_id       :bigint
#  store_chain_id :bigint
#
# Indexes
#
#  index_stores_on_brand_id        (brand_id)
#  index_stores_on_status          (status)
#  index_stores_on_store_chain_id  (store_chain_id)
#
# Foreign Keys
#
#  fk_rails_...  (brand_id => brands.id)
#  fk_rails_...  (store_chain_id => store_chains.id)
#
FactoryBot.define do
  factory :store do
    name { Faker::Company.name }
    phone_number { Faker::PhoneNumber.phone_number }
    status { :requested }
    association :brand

    trait :active do
      status { :active }
    end
  end
end
