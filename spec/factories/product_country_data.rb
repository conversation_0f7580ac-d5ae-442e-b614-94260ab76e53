# == Schema Information
#
# Table name: product_country_data
#
#  id            :bigint           not null, primary key
#  msrp          :decimal(10, 2)
#  points_cost   :integer
#  points_earned :integer
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#  country_id    :bigint           not null
#  product_id    :bigint           not null
#
# Indexes
#
#  index_product_country_data_on_country_id                 (country_id)
#  index_product_country_data_on_product_id                 (product_id)
#  index_product_country_data_on_product_id_and_country_id  (product_id,country_id) UNIQUE
#
# Foreign Keys
#
#  fk_rails_...  (country_id => countries.id)
#  fk_rails_...  (product_id => products.id)
#
FactoryBot.define do
  factory :product_country_datum do
    association :product
    association :country, factory: :country, strategy: :build
    msrp { Faker::Commerce.price(range: 1.0..100.0) }
    points_earned { Faker::Number.between(from: 0, to: 500) }
    points_cost { Faker::Number.between(from: 0, to: 500) }
  end
end
