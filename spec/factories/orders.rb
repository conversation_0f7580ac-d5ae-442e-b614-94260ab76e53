# spec/factories/orders.rb
# == Schema Information
#
# Table name: orders
#
#  id               :bigint           not null, primary key
#  points           :integer          not null
#  shipping_address :string
#  shipping_type    :string           not null
#  status           :integer          default("pending"), not null
#  total            :decimal(10, 2)   default(0.0), not null
#  created_at       :datetime         not null
#  updated_at       :datetime         not null
#  user_id          :bigint           not null
#
# Indexes
#
#  index_orders_on_user_id  (user_id)
#
# Foreign Keys
#
#  fk_rails_...  (user_id => users.id)
#
FactoryBot.define do
  factory :order do
    association :user
    status { :pending }
    total { 100.0 }
    shipping_type { "user" }
    shipping_address { "123 Main St, City, State 12345" }
    points { 50 }
    created_at { Time.current }
    updated_at { Time.current }

    trait :approved do
      status { :approved }
    end

    trait :rejected do
      status { :rejected }
    end

    trait :ship_to_store do
      shipping_type { "store" }
      shipping_address { nil }
    end

    trait :ship_to_user do
      shipping_type { "user" }
      shipping_address { "123 Main St, City, State 12345" }
    end

    trait :with_line_items do
      after(:create) do |order|
        create_list(:line_item, 2, :in_order, order: order)
        # Update total based on line items
        order.update!(total: order.line_items.sum(&:total_price))
      end
    end

    trait :high_points do
      points { 500 }
    end

    trait :low_points do
      points { 10 }
    end
  end
end
