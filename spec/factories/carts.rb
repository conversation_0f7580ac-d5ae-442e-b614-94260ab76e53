# == Schema Information
#
# Table name: carts
#
#  id         :bigint           not null, primary key
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  user_id    :bigint
#
# Indexes
#
#  index_carts_on_user_id  (user_id)
#
# Foreign Keys
#
#  fk_rails_...  (user_id => users.id)
#
FactoryBot.define do
  factory :cart do
    user { nil }

    trait :with_user do
      association :user
    end

    trait :guest do
      user { nil }
    end

    trait :with_line_items do
      after(:create) do |cart|
        create_list(:line_item, 2, cart: cart)
      end
    end

    trait :empty do
      # Default state - no line items
    end
  end
end
