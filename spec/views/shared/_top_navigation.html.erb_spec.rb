require "rails_helper"

RSpec.describe "shared/_top_navigation", type: :view do
  let(:user) { create(:user) }
  let(:wallet) do
    user.wallet.update(points: 150)
    user.wallet
  end

  before do
    allow(view).to receive(:current_user).and_return(user)
    user.wallet = wallet
  end

  context "with default parameters" do
    it "displays the <PERSON>eiss logo and default title" do
      render partial: "shared/top_navigation"

      expect(rendered).to have_css("img[alt='Zeiss Logo']")
      expect(rendered).to have_content("ZeissPoints")
      # Only check that the back button (link with arrow SVG) is not present
      expect(rendered).not_to have_css("a > svg.w-5.h-5")
    end
  end

  context "with custom title and subtitle" do
    it "displays custom title and subtitle" do
      render partial: "shared/top_navigation", locals: {
        title: "Add Sale",
        subtitle: "Zeiss Products"
      }

      expect(rendered).to have_content("Add Sale")
      expect(rendered).to have_content("Zeiss Products")
    end
  end

  context "with back button enabled" do
    it "displays back button instead of logo" do
      render partial: "shared/top_navigation", locals: {
        show_back_button: true,
        back_path: "/dashboard",
        title: "Add Sale"
      }

      expect(rendered).to have_css("a[href='/dashboard']")
      expect(rendered).to have_css("svg") # Back arrow icon
      expect(rendered).not_to have_css("img[alt='Zeiss Logo']")
    end
  end

  it "displays user's points" do
    render partial: "shared/top_navigation"

    expect(rendered).to have_content("150 pts")
    expect(rendered).to have_css(".bg-zeiss-50")
  end

  it "displays profile menu button" do
    render partial: "shared/top_navigation"

    expect(rendered).to have_css("button")
    expect(rendered).to have_css("svg") # Profile icon
  end

  context "when user has no wallet" do
    before do
      user.wallet = nil
    end

    it "displays 0 points" do
      render partial: "shared/top_navigation"

      expect(rendered).to have_content("0 pts")
    end
  end
end
