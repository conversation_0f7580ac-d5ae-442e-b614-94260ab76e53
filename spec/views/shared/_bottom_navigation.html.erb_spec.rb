require "rails_helper"

RSpec.describe "shared/_bottom_navigation", type: :view do
  before do
    # Mock the path helpers
    allow(view).to receive(:root_path).and_return("/")
    allow(view).to receive(:new_sale_path).and_return("/sales/new")
    allow(view).to receive(:orders_path).and_return("/orders")
  end

  context "with dashboard active" do
    it "highlights dashboard tab" do
      render partial: "shared/bottom_navigation", locals: {active_tab: "dashboard"}

      expect(rendered).to have_css("a[href='/']")
      expect(rendered).to have_css(".text-zeiss-600", text: "Dashboard")
      expect(rendered).to have_css(".text-gray-400", text: "Add Sale")
      expect(rendered).to have_css(".text-gray-400", text: "Orders")
      expect(rendered).to have_css(".text-gray-400", text: "Profile")
    end
  end

  context "with sales active" do
    it "highlights sales tab" do
      render partial: "shared/bottom_navigation", locals: {active_tab: "sales"}

      expect(rendered).to have_css(".text-gray-400", text: "Dashboard")
      expect(rendered).to have_css(".text-zeiss-600", text: "Add Sale")
      expect(rendered).to have_css(".text-gray-400", text: "Orders")
      expect(rendered).to have_css(".text-gray-400", text: "Profile")
    end
  end

  context "with orders active" do
    it "highlights orders tab" do
      render partial: "shared/bottom_navigation", locals: {active_tab: "orders"}

      expect(rendered).to have_css(".text-gray-400", text: "Dashboard")
      expect(rendered).to have_css(".text-gray-400", text: "Add Sale")
      expect(rendered).to have_css(".text-zeiss-600", text: "Orders")
      expect(rendered).to have_css(".text-gray-400", text: "Profile")
    end
  end

  context "with no active tab specified" do
    it "shows all tabs as inactive" do
      render partial: "shared/bottom_navigation"

      expect(rendered).to have_css(".text-gray-400", text: "Dashboard")
      expect(rendered).to have_css(".text-gray-400", text: "Add Sale")
      expect(rendered).to have_css(".text-gray-400", text: "Orders")
      expect(rendered).to have_css(".text-gray-400", text: "Profile")
    end
  end

  it "includes all navigation links" do
    render partial: "shared/bottom_navigation"

    expect(rendered).to have_css("a[href='/']", text: "Dashboard")
    expect(rendered).to have_css("a[href='/sales/new']", text: "Add Sale")
    expect(rendered).to have_css("a[href='/orders']", text: "Orders")
    expect(rendered).to have_css("a[href='#']", text: "Profile")
  end

  it "has proper grid layout structure" do
    render partial: "shared/bottom_navigation"

    expect(rendered).to have_css(".grid-cols-4")
    expect(rendered).to have_css(".h-16")
    expect(rendered).to have_css(".fixed.bottom-0")
  end
end
