import { Application } from "@hotwired/stimulus"
import PointsController from "../../../app/javascript/controllers/points_controller"

// Mock fetch
global.fetch = jest.fn()

describe("PointsController", () => {
  let application
  let controller
  let element

  beforeEach(() => {
    // Setup DOM
    document.body.innerHTML = `
      <div data-controller="points">
        <select data-points-target="product">
          <option value="">Select a product</option>
          <option value="1">Product 1</option>
          <option value="2">Product 2</option>
        </select>
        <span data-points-target="value">-</span>
        <input type="hidden" data-points-target="field" name="points" />
      </div>
    `
    
    element = document.querySelector('[data-controller="points"]')
    
    // Setup Stimulus application
    application = Application.start()
    application.register("points", PointsController)
    
    controller = application.getControllerForElementAndIdentifier(element, "points")
    
    // Clear mocks
    jest.clearAllMocks()
  })

  afterEach(() => {
    application.stop()
    document.body.innerHTML = ""
    fetch.mockClear()
  })

  describe("initialization", () => {
    it("connects successfully", () => {
      expect(controller).toBeDefined()
    })

    it("sets up product change listener", () => {
      const productTarget = controller.productTarget
      expect(productTarget).toBeTruthy()
      
      // Verify event listener is attached by triggering change
      const updatePointsSpy = jest.spyOn(controller, 'updatePoints')
      productTarget.dispatchEvent(new Event('change'))
      expect(updatePointsSpy).toHaveBeenCalled()
    })
  })

  describe("updatePoints", () => {
    it("clears points when no product is selected", async () => {
      controller.productTarget.value = ""
      
      await controller.updatePoints()
      
      expect(controller.valueTarget.textContent).toBe('-')
      expect(fetch).not.toHaveBeenCalled()
    })

    it("fetches and displays points for selected product", async () => {
      const mockResponse = { points: 150 }
      fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse
      })

      controller.productTarget.value = "1"
      
      await controller.updatePoints()
      
      expect(fetch).toHaveBeenCalledWith('/sales/points?product_id=1')
      expect(controller.valueTarget.textContent).toBe('150')
      expect(controller.fieldTarget.value).toBe('150')
    })

    it("handles fetch errors gracefully", async () => {
      fetch.mockResolvedValueOnce({
        ok: false,
        statusText: 'Not Found'
      })

      const consoleSpy = jest.spyOn(console, 'error').mockImplementation()
      controller.productTarget.value = "1"
      
      await controller.updatePoints()
      
      expect(controller.valueTarget.textContent).toBe('-')
      expect(consoleSpy).toHaveBeenCalledWith('[Stimulus] fetch error:', 'Not Found')
      consoleSpy.mockRestore()
    })

    it("handles network errors gracefully", async () => {
      const networkError = new Error('Network error')
      fetch.mockRejectedValueOnce(networkError)

      const consoleSpy = jest.spyOn(console, 'error').mockImplementation()
      controller.productTarget.value = "1"
      
      await controller.updatePoints()
      
      expect(controller.valueTarget.textContent).toBe('-')
      expect(consoleSpy).toHaveBeenCalledWith('[Stimulus] fetch error:', networkError)
      consoleSpy.mockRestore()
    })

    it("logs debug information", async () => {
      const mockResponse = { points: 100 }
      fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse
      })

      const consoleSpy = jest.spyOn(console, 'debug').mockImplementation()
      controller.productTarget.value = "2"
      
      await controller.updatePoints()
      
      expect(consoleSpy).toHaveBeenCalledWith('[Stimulus] updatePoints called, productId:', '2')
      expect(consoleSpy).toHaveBeenCalledWith('[Stimulus] fetch response:', expect.any(Object))
      expect(consoleSpy).toHaveBeenCalledWith('[Stimulus] response data:', mockResponse)
      consoleSpy.mockRestore()
    })
  })

  describe("product selection integration", () => {
    it("updates points when product selection changes", async () => {
      const mockResponse = { points: 75 }
      fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse
      })

      // Simulate user selecting a product
      controller.productTarget.value = "1"
      controller.productTarget.dispatchEvent(new Event('change'))
      
      // Wait for async operation
      await new Promise(resolve => setTimeout(resolve, 0))
      
      expect(fetch).toHaveBeenCalledWith('/sales/points?product_id=1')
      expect(controller.valueTarget.textContent).toBe('75')
      expect(controller.fieldTarget.value).toBe('75')
    })
  })

  describe("without product target", () => {
    beforeEach(() => {
      // Setup DOM without product target
      document.body.innerHTML = `
        <div data-controller="points">
          <span data-points-target="value">-</span>
          <input type="hidden" data-points-target="field" name="points" />
        </div>
      `
      
      element = document.querySelector('[data-controller="points"]')
      application.stop()
      application = Application.start()
      application.register("points", PointsController)
      controller = application.getControllerForElementAndIdentifier(element, "points")
    })

    it("handles missing product target gracefully", async () => {
      await controller.updatePoints()
      
      expect(controller.valueTarget.textContent).toBe('-')
      expect(fetch).not.toHaveBeenCalled()
    })
  })
})
