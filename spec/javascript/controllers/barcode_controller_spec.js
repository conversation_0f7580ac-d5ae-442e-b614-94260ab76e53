import { Application } from "@hotwired/stimulus"
import BarcodeController from "../../../app/javascript/controllers/barcode_controller"

// Mock Quagga2
const mockQuagga = {
  init: jest.fn(),
  start: jest.fn(),
  stop: jest.fn(),
  onDetected: jest.fn(),
  onProcessed: jest.fn(),
  offDetected: jest.fn(),
  offProcessed: jest.fn(),
  ResultCollector: {
    create: jest.fn(() => ({
      getResults: jest.fn(() => [])
    }))
  },
  registerResultCollector: jest.fn(),
  CameraAccess: {
    getActiveTrack: jest.fn(() => ({
      getCapabilities: jest.fn(() => ({ torch: true })),
      getConstraints: jest.fn(() => ({ torch: false })),
      applyConstraints: jest.fn()
    }))
  }
}

// Mock the loadQuagga function
jest.mock("../../../app/javascript/load_quagga.js", () => ({
  loadQuagga: jest.fn(() => Promise.resolve(mockQuagga))
}))

// Mock navigator.mediaDevices
Object.defineProperty(navigator, 'mediaDevices', {
  writable: true,
  value: {
    getUserMedia: jest.fn(() => Promise.resolve({
      getTracks: () => [{ stop: jest.fn() }]
    }))
  }
})

describe("BarcodeController", () => {
  let application
  let controller
  let element

  beforeEach(() => {
    // Setup DOM
    document.body.innerHTML = `
      <div data-controller="barcode"
           data-barcode-readers-value='["code_128_reader", "ean_reader"]'
           data-barcode-frequency-value="10">
        <div style="position: relative;">
          <input type="text" data-barcode-target="input" />
        </div>
      </div>
    `

    element = document.querySelector('[data-controller="barcode"]')

    // Setup Stimulus application
    application = Application.start()
    application.register("barcode", BarcodeController)

    controller = application.getControllerForElementAndIdentifier(element, "barcode")

    // Clear mocks
    jest.clearAllMocks()
  })

  afterEach(() => {
    application.stop()
    document.body.innerHTML = ""
  })

  describe("initialization", () => {
    it("connects successfully", () => {
      expect(controller).toBeDefined()
      expect(controller.isScanning).toBe(false)
    })

    it("sets up scan button", () => {
      const scanButton = element.querySelector('.barcode-scan-btn')
      expect(scanButton).toBeTruthy()
      expect(scanButton.querySelector('svg')).toBeTruthy()
    })

    it("configures default values", () => {
      expect(controller.readersValue).toEqual(["code_128_reader", "ean_reader"])
      expect(controller.frequencyValue).toBe(10)
      expect(controller.halfSampleValue).toBe(true)
      expect(controller.patchSizeValue).toBe("medium")
    })
  })

  describe("camera permissions", () => {
    it("checks camera permissions successfully", async () => {
      const result = await controller.checkCameraPermissions()
      expect(result).toBe(true)
      expect(navigator.mediaDevices.getUserMedia).toHaveBeenCalledWith({
        video: { facingMode: "environment" }
      })
    })

    it("handles camera permission denial", async () => {
      navigator.mediaDevices.getUserMedia.mockRejectedValueOnce(new Error("Permission denied"))

      const result = await controller.checkCameraPermissions()
      expect(result).toBe(false)
    })

    it("handles missing camera API", async () => {
      const originalMediaDevices = navigator.mediaDevices
      delete navigator.mediaDevices

      const result = await controller.checkCameraPermissions()
      expect(result).toBe(false)

      navigator.mediaDevices = originalMediaDevices
    })
  })

  describe("scanner overlay", () => {
    beforeEach(() => {
      controller.createScannerOverlay()
    })

    afterEach(() => {
      if (controller.overlay) {
        document.body.removeChild(controller.overlay)
        controller.overlay = null
      }
    })

    it("creates overlay with proper structure", () => {
      expect(controller.overlay).toBeTruthy()
      expect(controller.overlay.querySelector('#barcode-scanner')).toBeTruthy()
      expect(controller.overlay.querySelector('.close-scanner')).toBeTruthy()
      expect(controller.overlay.querySelector('.toggle-torch')).toBeTruthy()
      expect(controller.overlay.querySelector('.manual-entry')).toBeTruthy()
    })

    it("prevents body scroll", () => {
      expect(document.body.style.overflow).toBe('hidden')
    })

    it("handles close button click", () => {
      const closeButton = controller.overlay.querySelector('.close-scanner')
      const stopScannerSpy = jest.spyOn(controller, 'stopScanner')

      closeButton.click()
      expect(stopScannerSpy).toHaveBeenCalled()
    })
  })

  describe("barcode validation", () => {
    it("validates correct barcodes", () => {
      expect(controller.isValidBarcode("1234567890")).toBe(true)
      expect(controller.isValidBarcode("ABC123DEF456")).toBe(true)
      expect(controller.isValidBarcode("123456789012345")).toBe(true)
    })

    it("rejects invalid barcodes", () => {
      expect(controller.isValidBarcode("")).toBe(false)
      expect(controller.isValidBarcode("123")).toBe(false) // Too short
      expect(controller.isValidBarcode("1234567890123456789")).toBe(false) // Too long
      expect(controller.isValidBarcode("123@456")).toBe(false) // Invalid characters
      expect(controller.isValidBarcode(null)).toBe(false)
      expect(controller.isValidBarcode(undefined)).toBe(false)
    })
  })

  describe("result processing", () => {
    it("finds most common code from results", () => {
      const codes = ["123456", "123456", "789012", "123456"]
      const result = controller.getMostCommonCode(codes)
      expect(result).toBe("123456")
    })

    it("requires minimum occurrences", () => {
      const codes = ["123456", "789012", "345678"]
      const result = controller.getMostCommonCode(codes)
      expect(result).toBe(null)
    })

    it("handles empty results", () => {
      const result = controller.getMostCommonCode([])
      expect(result).toBe(null)
    })
  })

  describe("successful scan handling", () => {
    let input

    beforeEach(() => {
      input = element.querySelector('input')
      controller.createScannerOverlay()
    })

    afterEach(() => {
      if (controller.overlay) {
        document.body.removeChild(controller.overlay)
        controller.overlay = null
      }
    })

    it("updates input value", () => {
      const testCode = "1234567890"
      controller.handleSuccessfulScan(testCode)

      expect(input.value).toBe(testCode)
    })

    it("dispatches input events", () => {
      const inputSpy = jest.fn()
      const changeSpy = jest.fn()

      input.addEventListener('input', inputSpy)
      input.addEventListener('change', changeSpy)

      controller.handleSuccessfulScan("1234567890")

      expect(inputSpy).toHaveBeenCalled()
      expect(changeSpy).toHaveBeenCalled()
    })

    it("shows success animation", () => {
      controller.handleSuccessfulScan("1234567890")

      const statusEl = controller.overlay.querySelector('.scanning-status')
      expect(statusEl.textContent).toBe("✓ Barcode detected!")
    })
  })

  describe("torch functionality", () => {
    beforeEach(() => {
      controller.createScannerOverlay()
    })

    afterEach(() => {
      if (controller.overlay) {
        document.body.removeChild(controller.overlay)
        controller.overlay = null
      }
    })

    it("toggles torch when supported", async () => {
      await controller.toggleTorch()

      const track = mockQuagga.CameraAccess.getActiveTrack()
      expect(track.applyConstraints).toHaveBeenCalledWith({
        advanced: [{ torch: true }]
      })
    })

    it("handles torch not supported", async () => {
      mockQuagga.CameraAccess.getActiveTrack.mockReturnValueOnce({
        getCapabilities: () => ({}),
        getConstraints: () => ({}),
        applyConstraints: jest.fn()
      })

      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation()
      await controller.toggleTorch()

      expect(consoleSpy).toHaveBeenCalledWith("Torch not supported:", expect.any(Error))
      consoleSpy.mockRestore()
    })
  })

  describe("error handling", () => {
    it("shows error messages", () => {
      const testMessage = "Test error message"
      controller.showError(testMessage)

      const toast = document.querySelector('.fixed.top-4')
      expect(toast).toBeTruthy()
      expect(toast.textContent).toBe(testMessage)
      expect(toast.className).toContain('bg-red-600')
    })

    it("auto-removes error messages", (done) => {
      controller.showError("Test error")

      setTimeout(() => {
        const toast = document.querySelector('.fixed.top-4')
        expect(toast).toBeFalsy()
        done()
      }, 5100)
    }, 6000)
  })

  describe("cleanup", () => {
    it("stops scanner on disconnect", () => {
      const stopScannerSpy = jest.spyOn(controller, 'stopScanner')
      controller.disconnect()
      expect(stopScannerSpy).toHaveBeenCalled()
    })

    it("cleans up resources when stopping", () => {
      controller.isScanning = true
      controller.createScannerOverlay()

      controller.stopScanner()

      expect(mockQuagga.stop).toHaveBeenCalled()
      expect(mockQuagga.offDetected).toHaveBeenCalled()
      expect(mockQuagga.offProcessed).toHaveBeenCalled()
      expect(document.body.style.overflow).toBe('')
      expect(controller.isScanning).toBe(false)
    })
  })

  describe("Quagga integration", () => {
    it("initializes Quagga with correct configuration", async () => {
      controller.createScannerOverlay()

      const { loadQuagga } = require("../../../app/javascript/load_quagga.js")
      await controller.initializeQuagga(await loadQuagga())

      expect(mockQuagga.init).toHaveBeenCalledWith(
        expect.objectContaining({
          inputStream: expect.objectContaining({
            name: "Live",
            type: "LiveStream",
            constraints: expect.objectContaining({
              facingMode: "environment"
            })
          }),
          frequency: 10,
          decoder: expect.objectContaining({
            readers: ["code_128_reader", "ean_reader"]
          }),
          locate: true
        }),
        expect.any(Function)
      )
    })

    it("sets up result collector", async () => {
      const { loadQuagga } = require("../../../app/javascript/load_quagga.js")
      const Quagga = await loadQuagga()

      controller.setupResultCollector(Quagga)

      expect(mockQuagga.ResultCollector.create).toHaveBeenCalledWith(
        expect.objectContaining({
          capture: false,
          capacity: 20,
          blacklist: [],
          filter: expect.any(Function)
        })
      )
      expect(mockQuagga.registerResultCollector).toHaveBeenCalled()
    })
  })
})