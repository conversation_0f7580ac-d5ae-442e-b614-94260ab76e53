import { Application } from "@hotwired/stimulus"
import Hello<PERSON><PERSON>roll<PERSON> from "../../../app/javascript/controllers/hello_controller"

describe('HelloController', () => {
  let application
  let element

  beforeEach(() => {
    document.body.innerHTML = `<div data-controller="hello"></div>`
    application = Application.start()
    application.register("hello", HelloController)
    element = document.querySelector('[data-controller="hello"]')
  })

  it('connects', () => {
    expect(element).not.toBeNull()
  })
})
