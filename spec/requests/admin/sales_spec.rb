require "rails_helper"

RSpec.describe "Admin::Sales", type: :request do
  let(:store) { create(:store, :active) }
  let(:admin) { create(:user, role: :admin, status: :active, confirmed_at: Time.now, store: store) }
  let(:user) { create(:user, status: :active, store: store) }
  let(:product) { create(:product) }
  let!(:pending_sale) { create(:sale, user: user, product: product, status: :pending) }
  let!(:approved_sale) { create(:sale, user: user, product: product, status: :approved) }

  before do
    login_as(admin, scope: :user)
  end

  describe "GET /admin/sales" do
    it "returns http success" do
      get admin_sales_path
      expect(response).to have_http_status(:success)
    end

    it "lists sales with pagination" do
      get admin_sales_path
      expect(response).to have_http_status(:success)
      expect(assigns(:pagy)).to be_present
      expect(assigns(:sales)).to include(pending_sale, approved_sale)
    end

    it "filters sales by status" do
      get admin_sales_path, params: {q: {status_eq: "pending"}}
      expect(response).to have_http_status(:success)
      expect(assigns(:sales)).to include(pending_sale)
      expect(assigns(:sales)).not_to include(approved_sale)
    end

    it "searches sales by user email" do
      get admin_sales_path, params: {q: {user_email_cont: user.email.split("@").first}}
      expect(response).to have_http_status(:success)
      expect(assigns(:sales)).to include(pending_sale, approved_sale)
    end

    it "searches sales by product name" do
      get admin_sales_path, params: {q: {product_name_cont: product.name}}
      expect(response).to have_http_status(:success)
      expect(assigns(:sales)).to include(pending_sale, approved_sale)
    end
  end

  describe "GET /admin/sales/:id" do
    it "shows sale details" do
      get admin_sale_path(pending_sale)
      expect(response).to have_http_status(:success)
      expect(assigns(:sale)).to eq(pending_sale)
    end
  end

  describe "POST /admin/sales/:id/approve" do
    it "approves pending sales" do
      expect {
        post approve_admin_sale_path(pending_sale)
      }.to change { pending_sale.reload.status }.from("pending").to("approved")

      expect(response).to redirect_to(admin_sales_path)
      expect(flash[:notice]).to include("approved")
    end

    it "credits points to user wallet" do
      expect {
        post approve_admin_sale_path(pending_sale)
      }.to change { user.wallet.reload.balance }.by(pending_sale.points)
    end

    it "does not approve non-pending sales" do
      post approve_admin_sale_path(approved_sale)

      expect(response).to redirect_to(admin_sales_path)
      expect(flash[:alert]).to include("not pending")
    end
  end

  describe "POST /admin/sales/:id/reject" do
    it "rejects pending sales" do
      expect {
        post reject_admin_sale_path(pending_sale)
      }.to change { pending_sale.reload.status }.from("pending").to("rejected")

      expect(response).to redirect_to(admin_sales_path)
      expect(flash[:notice]).to include("rejected")
    end

    it "does not reject non-pending sales" do
      post reject_admin_sale_path(approved_sale)

      expect(response).to redirect_to(admin_sales_path)
      expect(flash[:alert]).to include("not pending")
    end
  end

  context "with non-admin user" do
    let(:regular_user) { create(:user, role: :regular, status: :active, store: store) }

    before do
      login_as(regular_user, scope: :user)
    end

    it "denies access to admin sales" do
      get admin_sales_path
      expect(response).to redirect_to(root_path)
      expect(flash[:alert]).to eq("Access denied. Admin privileges required.")
    end
  end
end
