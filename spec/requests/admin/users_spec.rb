require "rails_helper"

RSpec.describe "Admin::Users", type: :request do
  let(:store) { create(:store, :active) }
  let(:admin) { create(:user, role: :admin, status: :active, confirmed_at: Time.now, store: store) }
  let(:user) { create(:user, status: :active, email: "<EMAIL>", confirmed_at: Time.now, store: store) }

  before do
    login_as(admin, scope: :user)
  end

  describe "PATCH /admin/users/:id" do
    it "updates user information and confirms new email" do
      patch admin_user_path(user), params: {
        user: {
          email: "<EMAIL>",
          role: "admin",
          status: "active",
          store_id: store.id
        }
      }
      user.reload
      expect(user.unconfirmed_email).to eq("<EMAIL>")
      expect(user.role).to eq("admin")
      expect(response).to redirect_to(admin_user_path(user))

      # Extract confirmation token from the last sent email
      mail = ActionMailer::Base.deliveries.last
      token = mail.body.encoded.match(/confirmation_token=([^\"]+)/)[1]

      # Confirm the new email
      get user_confirmation_path(confirmation_token: token)
      user.reload
      expect(user.email).to eq("<EMAIL>")
      expect(user.unconfirmed_email).to be_nil
    end
  end
end
