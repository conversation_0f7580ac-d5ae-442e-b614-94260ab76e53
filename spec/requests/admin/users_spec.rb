require "rails_helper"

RSpec.describe "Admin::Users", type: :request do
  let(:store) { create(:store, :active) }
  let(:admin) { create(:user, role: :admin, status: :active, confirmed_at: Time.now, store: store) }
  let(:user) { create(:user, status: :active, email: "<EMAIL>", confirmed_at: Time.now, store: store) }

  before do
    login_as(admin, scope: :user)
  end

  describe "GET /admin/users" do
    let!(:active_user) { create(:user, status: :active, store: store) }
    let!(:inactive_user) { create(:user, status: :inactive, store: store) }

    it "returns http success" do
      get admin_users_path
      expect(response).to have_http_status(:success)
    end

    it "lists users with pagination" do
      get admin_users_path
      expect(response).to have_http_status(:success)
      expect(assigns(:pagy)).to be_present
      expect(assigns(:users)).to include(active_user, inactive_user)
    end

    it "filters users by status" do
      get admin_users_path, params: {q: {status_eq: "active"}}
      expect(response).to have_http_status(:success)
      expect(assigns(:users)).to include(active_user)
      expect(assigns(:users)).not_to include(inactive_user)
    end

    it "searches users by email" do
      get admin_users_path, params: {q: {email_cont: active_user.email.split("@").first}}
      expect(response).to have_http_status(:success)
      expect(assigns(:users)).to include(active_user)
    end

    it "filters users by role" do
      create(:user, role: :admin, email: "<EMAIL>", status: :active, confirmed_at: Time.now, store: store)
      get admin_users_path, params: {q: {role_eq: 1}}
      expect(response).to have_http_status(:success)
      emails = assigns(:users).map(&:email)
      expect(emails).to include("<EMAIL>")
    end
  end

  describe "GET /admin/users/:id" do
    let(:test_user) { create(:user, status: :active, store: store) }

    it "shows user details" do
      get admin_user_path(test_user)
      expect(response).to have_http_status(:success)
      expect(assigns(:user)).to eq(test_user)
    end
  end

  describe "PATCH /admin/users/:id" do
    it "updates user information and confirms new email" do
      patch admin_user_path(user), params: {
        user: {
          email: "<EMAIL>",
          role: "admin",
          status: "active",
          store_id: store.id
        }
      }
      user.reload
      expect(user.unconfirmed_email).to eq("<EMAIL>")
      expect(user.role).to eq("admin")
      expect(response).to redirect_to(admin_user_path(user))

      # Extract confirmation token from the last sent email
      mail = ActionMailer::Base.deliveries.last
      token = mail.body.encoded.match(/confirmation_token=([^\"]+)/)[1]

      # Confirm the new email
      get user_confirmation_path(confirmation_token: token)
      user.reload
      expect(user.email).to eq("<EMAIL>")
      expect(user.unconfirmed_email).to be_nil
    end

    it "updates user information without email confirmation" do
      test_admin = create(:user, role: :admin, email: "<EMAIL>", status: :active, confirmed_at: Time.now, store: store)
      test_user = create(:user, status: :active, email: "<EMAIL>", confirmed_at: Time.now, store: store)
      login_as test_admin, scope: :user
      patch admin_user_path(test_user), params: {
        user: {email: "<EMAIL>", role: "admin", status: "active", store_id: store.id}
      }
      follow_redirect!
      test_user.reload
      # Email changes require confirmation, so check unconfirmed_email instead
      expect(test_user.unconfirmed_email).to eq("<EMAIL>")
      expect(test_user.role).to eq("admin")
    end
  end

  describe "POST /admin/users/:id/activate" do
    let(:inactive_user) { create(:user, status: :inactive, store: store) }

    it "activates inactive users" do
      expect {
        post activate_admin_user_path(inactive_user)
      }.to change { inactive_user.reload.status }.from("inactive").to("active")

      expect(response).to redirect_to(admin_users_path)
      expect(flash[:notice]).to include("activated")
    end
  end

  describe "POST /admin/users/:id/deactivate" do
    let(:active_user) { create(:user, status: :active, store: store) }

    it "deactivates active users" do
      expect {
        post deactivate_admin_user_path(active_user)
      }.to change { active_user.reload.status }.from("active").to("inactive")

      expect(response).to redirect_to(admin_users_path)
      expect(flash[:notice]).to include("deactivated")
    end
  end
end
