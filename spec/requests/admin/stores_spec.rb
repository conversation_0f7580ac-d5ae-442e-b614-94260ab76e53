require "rails_helper"

RSpec.describe "Admin::Stores", type: :request do
  let(:admin_store) { create(:store, :active) }
  let(:admin) { create(:user, role: :admin, status: :active, confirmed_at: Time.now, store: admin_store) }
  let!(:active_store) { create(:store, status: :active) }
  let!(:requested_store) { create(:store, status: :requested) }

  before do
    login_as(admin, scope: :user)
  end

  describe "GET /admin/stores" do
    it "returns http success" do
      get admin_stores_path
      expect(response).to have_http_status(:success)
    end

    it "lists stores with pagination" do
      get admin_stores_path
      expect(response).to have_http_status(:success)
      expect(assigns(:pagy)).to be_present
      expect(assigns(:stores)).to include(active_store, requested_store)
    end

    it "filters stores by status" do
      get admin_stores_path, params: {q: {status_eq: "requested"}}
      expect(response).to have_http_status(:success)
      expect(assigns(:stores)).to include(requested_store)
      expect(assigns(:stores)).not_to include(active_store)
    end

    it "searches stores by name" do
      get admin_stores_path, params: {q: {name_cont: active_store.name}}
      expect(response).to have_http_status(:success)
      expect(assigns(:stores)).to include(active_store)
    end
  end

  describe "GET /admin/stores/:id" do
    it "shows store details" do
      get admin_store_path(active_store)
      expect(response).to have_http_status(:success)
      expect(assigns(:store)).to eq(active_store)
    end

    it "shows store users" do
      user = create(:user, store: active_store)
      get admin_store_path(active_store)
      expect(assigns(:users)).to include(user)
    end

    it "shows recent sales for store" do
      user = create(:user, store: active_store)
      product = create(:product)
      sale = create(:sale, user: user, product: product)

      get admin_store_path(active_store)
      expect(assigns(:recent_sales)).to include(sale)
    end
  end

  describe "GET /admin/stores/:id/edit" do
    it "shows edit form" do
      get edit_admin_store_path(active_store)
      expect(response).to have_http_status(:success)
      expect(assigns(:store)).to eq(active_store)
    end
  end

  describe "PATCH /admin/stores/:id" do
    it "updates store information" do
      patch admin_store_path(active_store), params: {
        store: {name: "Updated Store Name", status: "inactive"}
      }

      expect(response).to redirect_to(admin_store_path(active_store))
      expect(active_store.reload.name).to eq("Updated Store Name")
      expect(active_store.status).to eq("inactive")
      expect(flash[:notice]).to include("updated successfully")
    end

    it "handles validation errors" do
      patch admin_store_path(active_store), params: {
        store: {name: ""}  # Invalid - name is required
      }

      expect(response).to have_http_status(:unprocessable_entity)
      expect(response).to render_template(:edit)
    end
  end

  describe "POST /admin/stores/:id/approve" do
    it "approves requested stores" do
      expect {
        post approve_admin_store_path(requested_store)
      }.to change { requested_store.reload.status }.from("requested").to("active")

      expect(response).to redirect_to(admin_stores_path)
      expect(flash[:notice]).to include("approved")
    end
  end

  describe "POST /admin/stores/:id/activate" do
    it "activates inactive stores" do
      inactive_store = create(:store, status: :inactive)

      expect {
        post activate_admin_store_path(inactive_store)
      }.to change { inactive_store.reload.status }.from("inactive").to("active")

      expect(response).to redirect_to(admin_stores_path)
      expect(flash[:notice]).to include("activated")
    end
  end

  describe "POST /admin/stores/:id/deactivate" do
    it "deactivates active stores" do
      expect {
        post deactivate_admin_store_path(active_store)
      }.to change { active_store.reload.status }.from("active").to("inactive")

      expect(response).to redirect_to(admin_stores_path)
      expect(flash[:notice]).to include("deactivated")
    end
  end

  context "with non-admin user" do
    let(:regular_user) { create(:user, role: :regular, status: :active, store: admin_store) }

    before do
      login_as(regular_user, scope: :user)
    end

    it "denies access to admin stores" do
      get admin_stores_path
      expect(response).to redirect_to(root_path)
      expect(flash[:alert]).to eq("Access denied. Admin privileges required.")
    end
  end
end
