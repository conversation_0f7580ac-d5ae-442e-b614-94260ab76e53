require "rails_helper"

RSpec.describe "Admin::Dashboard", type: :request do
  let(:store) { create(:store, :active) }
  let(:admin) { create(:user, role: :admin, status: :active, confirmed_at: Time.now, store: store) }
  let(:user) { create(:user, status: :active, store: store) }
  let(:product) { create(:product) }

  before do
    login_as(admin, scope: :user)
  end

  describe "Admin Base Controller" do
    it "includes Pagy backend for pagination" do
      expect(Admin::BaseController.included_modules).to include(Pagy::Backend)
    end
  end

  describe "GET /admin" do
    it "returns http success" do
      get admin_root_path
      expect(response).to have_http_status(:success)
    end

    it "displays dashboard statistics" do
      # Create test data
      create(:sale, user: user, product: product, status: :pending)
      create(:sale, user: user, product: product, status: :approved, points: 100)
      create(:user, status: :active, store: store)
      create(:store, status: :active)

      get admin_root_path

      expect(assigns(:pending_sales_count)).to eq(1)
      expect(assigns(:total_users)).to be >= 2 # admin + created user
      expect(assigns(:total_stores)).to be >= 1
      expect(assigns(:total_points_awarded)).to eq(100)
    end

    it "shows recent sales" do
      sale = create(:sale, user: user, product: product, status: :approved)

      get admin_root_path

      expect(assigns(:recent_sales)).to include(sale)
    end

    it "shows sales by status chart data" do
      create(:sale, user: user, product: product, status: :pending)
      create(:sale, user: user, product: product, status: :approved)

      get admin_root_path

      expect(assigns(:sales_by_status)).to include("pending" => 1, "approved" => 1)
    end

    it "shows recent activity" do
      sale = create(:sale, user: user, product: product, status: :approved, approved_by: admin.id, approved_at: Time.current)

      get admin_root_path

      expect(assigns(:recent_activity)).to include(sale)
    end

    it "shows enhanced recent admin activity" do
      # Approve a sale to create admin activity
      sale = create(:sale, user: user, product: product, status: :pending, sold_at: 1.day.ago)
      sale.update!(status: :approved, approved_by: admin.id, approved_at: Time.current)

      get admin_root_path
      expect(response).to have_http_status(:success)
      expect(assigns(:recent_activity)).to include(sale)
    end
  end

  context "with non-admin user" do
    let(:regular_user) { create(:user, role: :regular, status: :active, store: store) }

    before do
      login_as(regular_user, scope: :user)
    end

    it "denies access to admin dashboard" do
      get admin_root_path
      expect(response).to redirect_to(root_path)
      expect(flash[:alert]).to eq("Access denied. Admin privileges required.")
    end
  end

  context "with unauthenticated user" do
    before do
      logout(:user)
    end

    it "redirects to sign in" do
      get admin_root_path
      expect(response).to redirect_to(new_user_session_path)
    end
  end
end
