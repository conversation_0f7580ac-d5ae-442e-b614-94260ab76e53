require "warden/test/helpers"

RSpec.configure do |config|
  config.before(:suite) { Warden.test_mode! }
  config.after(:each) { Warden.test_reset! }
end
require "rails_helper"

RSpec.describe "Sales", type: :request do
  let!(:brand) { create(:brand) }
  let!(:store_chain) { create(:store_chain, brand: brand) }
  let!(:store) { create(:store, :active, store_chain: store_chain) }
  let!(:user) { create(:user, store: store, status: :active, confirmed_at: Time.current) }
  let!(:category) { create(:category, brand: brand) }
  let!(:product) { create(:product, category: category) }
  let!(:country) { create(:country, code: "CA", name: "Canada") }
  let!(:address) { create(:address, addressable: user, country: country) }

  before do
    login_as(user, scope: :user)
  end

  describe "GET /sales/new" do
    it "returns http success" do
      get new_sale_path
      expect(response).to have_http_status(:success)
    end

    it "renders the new sale form with product select" do
      get new_sale_path
      expect(response.body).to include("Product")
      expect(response.body).to include("Serial Number")
      expect(response.body).to include("Sale Date")
    end

    it "redirects to sign in if user has no store assigned" do
      user.update(store: nil)
      get new_sale_path
      expect(response).to redirect_to(new_user_session_path)
    end

    it "allows admin users without store assignment" do
      admin_user = create(:user, role: :admin)
      login_as(admin_user, scope: :user)
      get new_sale_path
      expect(response).to have_http_status(:success)
    end
  end

  describe "POST /sales" do
    let!(:product_country_datum) { create(:product_country_datum, product: product, country: country, points_earned: 100) }

    before do
      address # ensure address exists
    end

    it "creates a new sale successfully" do
      Timecop.freeze(Time.zone.local(2023, 1, 1, 12, 0, 0)) do
        expect {
          post sales_path, params: {
            sale: {
              product_id: product.id,
              serial_number: "TEST123456",
              sold_at: Date.current,
              notes: "Test sale"
            }
          }
        }.to change(Sale, :count).by(1)

        sale = Sale.last
        expect(sale.user).to eq(user)
        expect(sale.product).to eq(product)
        expect(sale.points).to eq(100)
        expect(sale.status).to eq("pending")
        expect(response).to redirect_to(root_path)
      end
    end

    it "sets points from product country data" do
      Timecop.freeze(Time.zone.local(2023, 1, 1, 12, 0, 0)) do
        post sales_path, params: {
          sale: {
            product_id: product.id,
            serial_number: "TEST123456",
            sold_at: Date.current
          }
        }

        sale = Sale.last
        expect(sale.points).to eq(100)
      end
    end

    it "handles validation errors" do
      Timecop.freeze(Time.zone.local(2023, 1, 1, 12, 0, 0)) do
        post sales_path, params: {
          sale: {
            product_id: product.id,
            serial_number: "", # Invalid - required
            sold_at: Date.current
          }
        }

        expect(Sale.count).to eq(0)
        expect(response).to have_http_status(:unprocessable_entity)
      end
    end

    it "allows past sale dates" do
      Timecop.freeze(Time.zone.local(2023, 1, 1, 12, 0, 0)) do
        expect {
          post sales_path, params: {
            sale: {
              product_id: product.id,
              serial_number: "TEST123456",
              sold_at: 1.week.ago
            }
          }
        }.to change(Sale, :count).by(1)

        expect(response).to redirect_to(root_path)
      end
    end

    it "prevents future sale dates" do
      post sales_path, params: {
        sale: {
          product_id: product.id,
          serial_number: "TEST123456",
          sold_at: 1.day.from_now
        }
      }
      expect(Sale.count).to eq(0)
      expect(response).to have_http_status(:unprocessable_entity)
    end
  end

  describe "GET /sales/points" do
    let!(:product_country_datum) { create(:product_country_datum, product: product, country: country, points_earned: 150) }

    before do
      address # ensure address exists
    end

    it "returns points for a product" do
      get points_sales_path, params: {product_id: product.id}, as: :json
      expect(response).to have_http_status(:success)
      json_response = JSON.parse(response.body)
      expect(json_response["points"]).to eq(150)
    end

    it "returns dash for non-existent product" do
      get points_sales_path, params: {product_id: 99999}, as: :json
      expect(response).to have_http_status(:success)
      json_response = JSON.parse(response.body)
      expect(json_response["points"]).to eq("-")
    end

    it "uses user's country for points calculation" do
      us_country = create(:country, code: "US", name: "United States-#{SecureRandom.hex(4)}")
      create(:product_country_datum, product: product, country: us_country, points_earned: 200)
      get points_sales_path, params: {product_id: product.id}, as: :json
      json_response = JSON.parse(response.body)
      expect(json_response["points"]).to eq(150)
    end
  end
end
