require "rails_helper"

RSpec.describe "Dashboard", type: :request do
  let(:user) { create(:user) }
  let(:product) { create(:product) }

  before do
    login_as(user, scope: :user)
  end

  describe "GET /dashboard" do
    it "returns http success" do
      get dashboard_path
      expect(response).to have_http_status(:success)
    end

    it "assigns recent sales" do
      # Create sales for the user
      5.times do |i|
        create(:sale, user: user, product: product, sold_at: i.days.ago - 2.hours)
      end

      # Create a sale for another user (should not be included)
      other_user = create(:user, email: "<EMAIL>")
      create(:sale, user: other_user, product: product, sold_at: 2.hours.ago)

      get dashboard_path

      expect(assigns(:recent_sales).count).to eq(5)
      expect(assigns(:recent_sales)).to all(have_attributes(user: user))
    end

    it "limits recent sales to 5" do
      # Create 7 sales
      7.times do |i|
        create(:sale, user: user, product: product, sold_at: i.days.ago - 2.hours)
      end

      get dashboard_path

      expect(assigns(:recent_sales).count).to eq(5)
    end

    it "orders recent sales by sold_at descending" do
      sale1 = create(:sale, user: user, product: product, sold_at: 3.days.ago - 2.hours)
      sale2 = create(:sale, user: user, product: product, sold_at: 1.day.ago - 2.hours)
      sale3 = create(:sale, user: user, product: product, sold_at: 2.days.ago - 2.hours)

      get dashboard_path

      recent_sales = assigns(:recent_sales)
      expect(recent_sales.first).to eq(sale2)  # Most recent
      expect(recent_sales.second).to eq(sale3)
      expect(recent_sales.third).to eq(sale1)  # Oldest
    end

    it "assigns recent orders" do
      # Create orders for the user
      3.times do |i|
        create(:order, user: user, product: product, created_at: i.days.ago, points: 10, shipping_type: "standard")
      end

      # Create an order for another user (should not be included)
      other_user = create(:user, email: "<EMAIL>")
      create(:order, user: other_user, product: product, points: 10, shipping_type: "standard")

      get dashboard_path

      expect(assigns(:recent_orders).count).to eq(3)
      expect(assigns(:recent_orders)).to all(have_attributes(user: user))
    end

    it "limits recent orders to 5" do
      # Create 7 orders
      7.times do |i|
        create(:order, user: user, product: product, created_at: i.days.ago, points: 10, shipping_type: "standard")
      end

      get dashboard_path

      expect(assigns(:recent_orders).count).to eq(5)
    end

    it "orders recent orders by created_at descending" do
      order1 = create(:order, user: user, product: product, created_at: 3.days.ago, points: 10, shipping_type: "standard")
      order2 = create(:order, user: user, product: product, created_at: 1.day.ago, points: 10, shipping_type: "standard")
      order3 = create(:order, user: user, product: product, created_at: 2.days.ago, points: 10, shipping_type: "standard")

      get dashboard_path

      recent_orders = assigns(:recent_orders)
      expect(recent_orders.first).to eq(order2)  # Most recent
      expect(recent_orders.second).to eq(order3)
      expect(recent_orders.third).to eq(order1)  # Oldest
    end
  end
end
