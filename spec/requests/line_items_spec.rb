require "warden/test/helpers"

RSpec.configure do |config|
  config.before(:suite) { Warden.test_mode! }
  config.after(:each) { Warden.test_reset! }
end
require "rails_helper"

RSpec.describe "LineItems", type: :request do
  let!(:brand) { create(:brand) }
  let!(:store_chain) { create(:store_chain) }
  let!(:store) { create(:store, :active, store_chain: store_chain, brand: brand) }
  let!(:user) { create(:user, store: store, status: :active, confirmed_at: Time.current) }
  let!(:category) { create(:category, brand: brand) }
  let!(:product) { create(:product, category: category) }
  let(:cart) { user.cart || user.create_cart }

  before { login_as(user, scope: :user) }

  describe "POST /line_items" do
    context "adding new product to cart" do
      it "creates a new line item" do
        expect {
          post line_items_path, params: {product_id: product.id}
        }.to change(LineItem, :count).by(1)
      end

      it "sets quantity to 1 for new item" do
        post line_items_path, params: {product_id: product.id}
        line_item = LineItem.last
        expect(line_item.quantity).to eq(1)
      end

      it "sets price from product" do
        post line_items_path, params: {product_id: product.id}
        line_item = LineItem.last
        expect(line_item.price).to be_present
      end

      it "associates with current cart" do
        post line_items_path, params: {product_id: product.id}
        line_item = LineItem.last
        expect(line_item.cart).to eq(cart)
      end

      it "redirects to cart with success message" do
        post line_items_path, params: {product_id: product.id}
        expect(response).to redirect_to(cart_path(cart))
        follow_redirect!
        expect(response.body).to include("Product added to cart")
      end
    end

    context "adding existing product to cart" do
      let!(:existing_line_item) { create(:line_item, cart: cart, product: product, quantity: 2, price: product.price) }

      it "does not create new line item" do
        expect {
          post line_items_path, params: {product_id: product.id}
        }.not_to change(LineItem, :count)
      end

      it "increments quantity of existing item" do
        expect {
          post line_items_path, params: {product_id: product.id}
        }.to change { existing_line_item.reload.quantity }.from(2).to(3)
      end

      it "redirects to cart with success message" do
        post line_items_path, params: {product_id: product.id}
        expect(response).to redirect_to(cart_path(cart))
        follow_redirect!
        expect(response.body).to include("Product added to cart")
      end
    end

    context "with invalid product" do
      it "handles non-existent product gracefully" do
        post line_items_path, params: {product_id: 99999}
        expect(response).to have_http_status(:not_found)
      end
    end

    context "when save fails" do
      before do
        allow_any_instance_of(LineItem).to receive(:save).and_return(false)
      end

      it "redirects to cart with error message" do
        post line_items_path, params: {product_id: product.id}
        expect(response).to redirect_to(cart_path(cart))
        follow_redirect!
        expect(response.body).to include("Could not add product")
      end
    end
  end

  describe "PATCH /line_items/:id" do
    let!(:line_item) { create(:line_item, cart: cart, product: product, quantity: 2, price: product.price) }

    context "with valid quantity" do
      it "updates the quantity" do
        patch line_item_path(line_item), params: {line_item: {quantity: 5}}
        expect(line_item.reload.quantity).to eq(5)
      end

      it "redirects to cart with success message" do
        patch line_item_path(line_item), params: {line_item: {quantity: 3}}
        expect(response).to redirect_to(cart_path(cart))
        follow_redirect!
        expect(response.body).to include("Cart updated")
      end
    end

    context "with invalid quantity" do
      it "does not update with zero quantity" do
        patch line_item_path(line_item), params: {line_item: {quantity: 0}}
        expect(line_item.reload.quantity).to eq(2)
      end

      it "does not update with negative quantity" do
        patch line_item_path(line_item), params: {line_item: {quantity: -1}}
        expect(line_item.reload.quantity).to eq(2)
      end

      it "redirects to cart with error message for invalid quantity" do
        patch line_item_path(line_item), params: {line_item: {quantity: 0}}
        expect(response).to redirect_to(cart_path(cart))
        follow_redirect!
        expect(response.body).to include("Could not update cart")
      end
    end

    context "updating other attributes" do
      it "can update price" do
        patch line_item_path(line_item), params: {line_item: {price: 12.99}}
        expect(line_item.reload.price).to eq(12.99)
      end
    end
  end

  describe "DELETE /line_items/:id" do
    let!(:line_item) { create(:line_item, cart: cart, product: product, quantity: 2, price: product.price) }

    it "destroys the line item" do
      expect {
        delete line_item_path(line_item)
      }.to change(LineItem, :count).by(-1)
    end

    it "redirects to cart" do
      delete line_item_path(line_item)
      expect(response).to redirect_to(cart_path(cart))
    end

    context "when line item does not exist" do
      it "handles missing line item gracefully" do
        delete line_item_path(99999)
        expect(response).to have_http_status(:not_found)
      end
    end
  end

  describe "cart association" do
    context "when user has no cart" do
      before do
        user.cart&.destroy
        user.reload
      end

      it "creates a cart when adding first item" do
        expect {
          post line_items_path, params: {product_id: product.id}
        }.to change(Cart, :count).by(1)
      end

      it "associates line item with new cart" do
        post line_items_path, params: {product_id: product.id}
        line_item = LineItem.last
        expect(line_item.cart).to eq(user.reload.cart)
      end
    end
  end

  describe "authentication requirements" do
    it "requires authentication to add items" do
      logout(:user)
      expect {
        post line_items_path, params: {product_id: product.id}
      }.not_to change(LineItem, :count)
      expect(response).to have_http_status(:found) # Redirect to sign in
    end

    it "requires authentication to update items" do
      line_item = create(:line_item, cart: cart, product: product, quantity: 1, price: 10.99)
      logout(:user)
      patch line_item_path(line_item), params: {line_item: {quantity: 3}}
      expect(line_item.reload.quantity).to eq(1)
      expect(response).to have_http_status(:found) # Redirect to sign in
    end

    it "requires authentication to remove items" do
      line_item = create(:line_item, cart: cart, product: product, quantity: 1, price: 10.99)
      logout(:user)
      expect {
        delete line_item_path(line_item)
      }.not_to change(LineItem, :count)
      expect(response).to have_http_status(:found) # Redirect to sign in
    end
  end

  describe "price handling" do
    it "preserves product price at time of adding to cart" do
      # Set up product with country data for pricing
      country = create(:country, code: "US", name: "United States")
      create(:product_country_datum, product: product, country: country, msrp: 29.99)

      post line_items_path, params: {product_id: product.id}
      line_item = LineItem.last
      original_price = line_item.price

      # Change product price
      product.product_country_data.first.update!(msrp: original_price + 10)

      expect(line_item.reload.price).to eq(original_price)
    end
  end
end
