require "warden/test/helpers"

RSpec.configure do |config|
  config.before(:suite) { Warden.test_mode! }
  config.after(:each) { Warden.test_reset! }
end
require "rails_helper"

RSpec.describe "Carts", type: :request do
  let!(:brand) { create(:brand) }
  let!(:store_chain) { create(:store_chain) }
  let!(:store) { create(:store, :active, store_chain: store_chain, brand: brand) }
  let!(:user) { create(:user, store: store, status: :active, confirmed_at: Time.current) }
  let!(:category) { create(:category, brand: brand) }
  let!(:product1) { create(:product, category: category) }
  let!(:product2) { create(:product, category: category) }

  describe "GET /carts/:id" do
    context "when user is signed in" do
      before { login_as(user, scope: :user) }

      context "with empty cart" do
        it "shows empty cart message" do
          get cart_path(user.cart || user.create_cart)
          expect(response).to have_http_status(:success)
          expect(response.body).to include("Your cart is empty")
        end
      end

      context "with items in cart" do
        let(:cart) { user.cart || user.create_cart }

        before do
          create(:line_item, cart: cart, product: product1, quantity: 2, price: 10.99)
          create(:line_item, cart: cart, product: product2, quantity: 1, price: 25.50)
        end

        it "displays cart items" do
          get cart_path(cart)
          expect(response).to have_http_status(:success)
          expect(response.body).to include(product1.name)
          expect(response.body).to include(product2.name)
        end

        it "shows total points required" do
          get cart_path(cart)
          # Assuming the view shows points instead of price
          expect(response.body).to include("points")
        end

        it "shows checkout button" do
          get cart_path(cart)
          expect(response.body).to include("Checkout")
        end

        it "shows quantity update forms" do
          get cart_path(cart)
          expect(response.body).to include("Update")
          expect(response.body).to include('type="number"')
        end

        it "shows remove buttons" do
          get cart_path(cart)
          expect(response.body).to include("Remove")
        end
      end
    end

    context "when user is not signed in" do
      it "requires authentication to view cart" do
        cart = create(:cart, user: nil)
        get cart_path(cart)
        expect(response).to have_http_status(:unauthorized)
      end
    end

    context "when cart does not exist" do
      before { login_as(user, scope: :user) }

      it "handles missing cart gracefully" do
        get cart_path(99999)
        # The current_cart method in ApplicationController handles missing carts
        # by creating a new one, so no error is raised
        expect(response).to have_http_status(:success)
      end
    end
  end

  describe "DELETE /carts/:id" do
    context "when user is signed in" do
      before { login_as(user, scope: :user) }

      let(:cart) { user.cart || user.create_cart }

      before do
        create(:line_item, cart: cart, product: product1, quantity: 2, price: 10.99)
      end

      it "destroys the cart" do
        expect {
          delete cart_path(cart)
        }.to change(Cart, :count).by(-1)
      end

      it "destroys associated line items" do
        line_item_count = cart.line_items.count
        expect {
          delete cart_path(cart)
        }.to change(LineItem, :count).by(-line_item_count)
      end

      it "clears session cart_id" do
        delete cart_path(cart)
        expect(session[:cart_id]).to be_nil
      end

      it "redirects to root path with notice" do
        delete cart_path(cart)
        expect(response).to redirect_to(root_path)
        follow_redirect!
        expect(response.body).to include("Cart was successfully emptied")
      end
    end

    context "when user is not signed in" do
      it "requires authentication to destroy cart" do
        cart = create(:cart, user: nil)
        expect {
          delete cart_path(cart)
        }.not_to change(Cart, :count)
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  describe "cart display formatting" do
    let(:cart) { user.cart || user.create_cart }

    before do
      login_as(user, scope: :user)
      create(:line_item, cart: cart, product: product1, quantity: 3, price: 10.99)
    end

    it "displays quantities correctly" do
      get cart_path(cart)
      expect(response.body).to include('value="3"')
    end

    it "displays individual item quantities" do
      get cart_path(cart)
      expect(response.body).to include('value="3"')
    end
  end

  describe "cart navigation" do
    let(:cart) { user.cart || user.create_cart }

    before do
      login_as(user, scope: :user)
      create(:line_item, cart: cart, product: product1, quantity: 1, price: 10.99)
    end

    it "provides checkout link when cart has items" do
      get cart_path(cart)
      expect(response.body).to include('action="/orders/new"')
    end
  end
end
