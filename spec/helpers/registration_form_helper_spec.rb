require "rails_helper"

RSpec.describe RegistrationFormHelper, type: :helper do
  describe "#state_options" do
    it "returns an array of [name, id] pairs for all states" do
      region = create(:region)
      state1 = create(:state, name: "Alberta", region: region)
      state2 = create(:state, name: "Ontario", region: region)
      expect(helper.state_options).to include(["Alberta", state1.id], ["Ontario", state2.id])
    end
  end
end
