require "warden/test/helpers"

RSpec.configure do |config|
  config.before(:suite) { Warden.test_mode! }
  config.after(:each) { Warden.test_reset! }
end
require "rails_helper"

RSpec.feature "Cart and Ordering Flow", type: :feature do
  let!(:brand) { create(:brand) }
  let!(:store_chain) { create(:store_chain) }
  let!(:store) { create(:store, :active, store_chain: store_chain, brand: brand) }
  let!(:user) { create(:user, store: store, status: :active, confirmed_at: Time.current) }
  let!(:category) { create(:category, brand: brand) }
  let!(:product1) { create(:product, category: category, name: "Test Camera") }
  let!(:product2) { create(:product, category: category, name: "Test Lens") }
  let!(:country) { create(:country, code: "CA", name: "Canada") }
  let!(:address) { create(:address, addressable: user, country: country) }

  before do
    # Create product country data for points
    create(:product_country_datum, product: product1, country: country, points_cost: 300, msrp: 299.99)
    create(:product_country_datum, product: product2, country: country, points_cost: 150, msrp: 149.99)

    login_as(user, scope: :user)
  end

  scenario "User adds products to cart and places order" do
    # Visit storefront
    visit orders_path
    expect(page).to have_content("Storefront")
    expect(page).to have_content("Test Camera")
    expect(page).to have_content("Test Lens")

    # Add first product to cart via line items
    expect {
      post line_items_path, params: {product_id: product1.id}
    }.to change(LineItem, :count).by(1)

    # Visit cart
    visit cart_path(user.cart)
    expect(page).to have_content("Your Cart")
    expect(page).to have_content("Test Camera")
    expect(page).to have_content("300 points")

    # Add second product to cart
    expect {
      post line_items_path, params: {product_id: product2.id}
    }.to change(LineItem, :count).by(1)

    # Visit cart again to see both items
    visit cart_path(user.cart)
    expect(page).to have_content("Test Camera")
    expect(page).to have_content("Test Lens")

    # Check total points calculation
    total_points = 300 + 150
    expect(page).to have_content("#{total_points} points")

    # Update quantity of first item
    within("tr", text: "Test Camera") do
      fill_in "line_item[quantity]", with: "2"
      click_button "Update"
    end

    # Verify quantity update
    expect(page).to have_content("Cart updated")

    # Check new total
    new_total = (299.99 * 2) + 149.99
    expect(page).to have_content("$#{sprintf("%.2f", new_total)}")

    # Proceed to checkout
    click_button "Checkout"
    expect(page).to have_content("Checkout")

    # Verify cart items are shown in checkout
    expect(page).to have_content("Test Camera")
    expect(page).to have_content("Test Lens")
    expect(page).to have_content("$#{sprintf("%.2f", new_total)}")

    # Fill out shipping information
    select "My Address", from: "order[shipping_type]"
    fill_in "order[shipping_address]", with: "123 Main Street, Test City, TC 12345"

    # Place order
    expect {
      click_button "Place Order"
    }.to change(Order, :count).by(1)

    # Verify order placement
    expect(page).to have_content("Order placed and pending approval")
    expect(current_path).to eq(orders_path)

    # Verify cart is empty after order
    order = Order.last
    expect(order.line_items.count).to eq(2)
    expect(order.total).to eq(new_total)
    expect(order.shipping_type).to eq("user")
    expect(order.shipping_address).to eq("123 Main Street, Test City, TC 12345")
    expect(order.status).to eq("pending")

    # Verify cart is destroyed
    expect(Cart.find_by(id: user.cart&.id)).to be_nil
  end

  scenario "User removes items from cart" do
    # Add items to cart first
    post line_items_path, params: {product_id: product1.id}
    post line_items_path, params: {product_id: product2.id}

    visit cart_path(user.cart)
    expect(page).to have_content("Test Camera")
    expect(page).to have_content("Test Lens")

    # Remove first item
    within("tr", text: "Test Camera") do
      click_button "Remove"
    end

    # Verify item removed
    expect(page).not_to have_content("Test Camera")
    expect(page).to have_content("Test Lens")
    expect(page).to have_content("$149.99")
  end

  scenario "User empties entire cart" do
    # Add items to cart
    post line_items_path, params: {product_id: product1.id}

    visit cart_path(user.cart)
    expect(page).to have_content("Test Camera")

    # Empty cart
    click_button "Empty Cart" # Assuming there's an empty cart button

    expect(page).to have_content("Cart was successfully emptied")
    expect(current_path).to eq(root_path)
  end

  scenario "User tries to checkout with empty cart" do
    visit new_order_path

    expect(page).to have_content("Your cart is empty")
    expect(current_path).to eq(cart_path(user.cart || user.create_cart))
  end

  scenario "User ships to store instead of address" do
    # Add item to cart
    post line_items_path, params: {product_id: product1.id}

    visit cart_path(user.cart)
    click_button "Checkout"

    # Select store shipping
    select "My Store", from: "order[shipping_type]"

    # Place order
    click_button "Place Order"

    # Verify order
    order = Order.last
    expect(order.shipping_type).to eq("store")
    expect(order.shipping_address).to be_blank
  end

  scenario "Order validation errors are handled" do
    # Add item to cart
    post line_items_path, params: {product_id: product1.id}

    visit cart_path(user.cart)
    click_button "Checkout"

    # Submit without required shipping type
    fill_in "order[shipping_address]", with: ""

    # This would need to be adjusted based on actual validation behavior
    # The form might prevent submission or show validation errors
  end

  scenario "Guest user cart functionality" do
    sign_out user

    # Create a guest cart session
    guest_cart = create(:cart, user: nil)

    # Simulate adding to guest cart
    expect {
      post line_items_path, params: {product_id: product1.id}
    }.to change(LineItem, :count).by(1)

    # Note: Guest checkout would require additional implementation
    # This test demonstrates the cart functionality works for guests
  end

  scenario "Cart persists across sessions for logged in users" do
    # Add item to cart
    post line_items_path, params: {product_id: product1.id}

    # Sign out and back in
    sign_out user
    sign_in user

    # Visit cart - should still have items
    visit cart_path(user.cart)
    expect(page).to have_content("Test Camera")
  end

  scenario "Multiple quantity updates work correctly" do
    # Add item to cart
    post line_items_path, params: {product_id: product1.id}

    visit cart_path(user.cart)

    # Update to quantity 3
    within("tr", text: "Test Camera") do
      fill_in "line_item[quantity]", with: "3"
      click_button "Update"
    end

    expect(page).to have_content("Cart updated")
    total = 299.99 * 3
    expect(page).to have_content("$#{sprintf("%.2f", total)}")

    # Update to quantity 1
    within("tr", text: "Test Camera") do
      fill_in "line_item[quantity]", with: "1"
      click_button "Update"
    end

    expect(page).to have_content("$299.99")
  end

  scenario "Adding same product multiple times increases quantity" do
    # Add product first time
    post line_items_path, params: {product_id: product1.id}

    visit cart_path(user.cart)
    expect(page).to have_field("line_item[quantity]", with: "1")

    # Add same product again
    post line_items_path, params: {product_id: product1.id}

    visit cart_path(user.cart)
    expect(page).to have_field("line_item[quantity]", with: "2")

    # Verify total reflects quantity
    total = 299.99 * 2
    expect(page).to have_content("$#{sprintf("%.2f", total)}")
  end

  scenario "Cart shows correct item totals" do
    # Add items with different quantities
    post line_items_path, params: {product_id: product1.id}
    post line_items_path, params: {product_id: product2.id}

    visit cart_path(user.cart)

    # Update first item to quantity 2
    within("tr", text: "Test Camera") do
      fill_in "line_item[quantity]", with: "2"
      click_button "Update"
    end

    # Check individual item totals
    camera_total = 299.99 * 2
    lens_total = 149.99 * 1
    grand_total = camera_total + lens_total

    expect(page).to have_content("$#{sprintf("%.2f", camera_total)}")
    expect(page).to have_content("$#{sprintf("%.2f", lens_total)}")
    expect(page).to have_content("Total: $#{sprintf("%.2f", grand_total)}")
  end

  scenario "Order preserves line item details correctly" do
    # Add items to cart
    post line_items_path, params: {product_id: product1.id}
    post line_items_path, params: {product_id: product2.id}

    # Update quantities
    cart = user.cart
    cart.line_items.find_by(product: product1).update!(quantity: 3)
    cart.line_items.find_by(product: product2).update!(quantity: 2)

    visit cart_path(cart)
    click_button "Checkout"

    select "My Address", from: "order[shipping_type]"
    fill_in "order[shipping_address]", with: "456 Test Ave"

    click_button "Place Order"

    # Verify order line items
    order = Order.last
    camera_line_item = order.line_items.find_by(product: product1)
    lens_line_item = order.line_items.find_by(product: product2)

    expect(camera_line_item.quantity).to eq(3)
    expect(camera_line_item.price).to eq(299.99)
    expect(lens_line_item.quantity).to eq(2)
    expect(lens_line_item.price).to eq(149.99)

    expected_total = (299.99 * 3) + (149.99 * 2)
    expect(order.total).to eq(expected_total)
  end
end
