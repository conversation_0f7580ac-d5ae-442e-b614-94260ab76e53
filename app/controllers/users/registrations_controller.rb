class Users::RegistrationsController < Devise::RegistrationsController
  before_action :ensure_store_selected, only: [:new, :create]

  def new
    build_resource({})
    resource.build_address
    respond_with resource
  end

  def create
    build_resource(sign_up_params)
    resource.store_id = session[:selected_store_id]

    yield resource if block_given?
    if resource.save
      if resource.active_for_authentication?
        set_flash_message! :notice, :signed_up
        sign_up(resource_name, resource)
        respond_with resource, location: after_sign_up_path_for(resource)
      else
        set_flash_message! :notice, :signed_up_but_unconfirmed
        expire_data_after_sign_in!
        respond_with resource, location: after_inactive_sign_up_path_for(resource)
      end
    else
      clean_up_passwords resource
      set_minimum_password_length
      respond_with resource
    end
  end

  private

  def sign_up_params
    params.require(:user).permit(:email, :password, :password_confirmation,
      address_attributes: [:street, :city, :state_id, :postal_code, :country_id])
  end

  def ensure_store_selected
    unless session[:selected_store_id].present?
      redirect_to search_stores_path, alert: "Please select your store before signing up."
    end
  end
end
