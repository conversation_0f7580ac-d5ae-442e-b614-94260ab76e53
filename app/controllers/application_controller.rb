class ApplicationController < ActionController::Base
  # Only allow modern browsers supporting webp images, web push, badges, import maps, CSS nesting, and CSS :has.
  allow_browser versions: :modern, block: :handle_outdated_browser

  helper_method :current_cart

  private

  def handle_outdated_browser
    Rails.logger.warn "Blocked browser: #{request.user_agent.inspect}"
    render file: Rails.root.join("public/406-unsupported-browser.html"), layout: false, status: :not_acceptable
  end

  def current_cart
    if user_signed_in?
      current_user.cart || current_user.create_cart
    else
      Cart.find(session[:cart_id])
    end
  rescue ActiveRecord::RecordNotFound
    cart = Cart.create
    session[:cart_id] = cart.id
    cart
  end

  protected

  def after_sign_in_path_for(resource)
    authenticated_root_path
  end
end
