class Admin::DashboardController < Admin::BaseController
  def index
    @pending_sales_count = Sale.pending.count
    @recent_sales = Sale.includes(:user, :product).order(created_at: :desc).limit(10)
    @total_users = User.count
    @total_stores = Store.count
    @total_points_awarded = Sale.approved.sum(:points)

    # Sales by status for chart
    @sales_by_status = Sale.group(:status).count

    # Recent activity
    @recent_activity = Sale.includes(:user, :product, :approved_by_admin)
      .where.not(status: :pending)
      .order(updated_at: :desc)
      .limit(5)
  end
end
