# frozen_string_literal: true

class Admin::SalesController < Admin::BaseController
  before_action :set_sale, only: [:show, :approve, :reject]

  def index
    @q = Sale.includes(:user, :product, :approved_by_admin).ransack(params[:q])
    @pagy, @sales = pagy(@q.result(distinct: true).order(created_at: :desc), items: 20)
  end

  def show
  end

  def approve
    if @sale.pending?
      @sale.update!(status: :approved, approved_by: current_user.id, approved_at: Time.current)
      @sale.user.wallet.credit(@sale.points)
      flash[:notice] = "Sale approved and #{@sale.points} points credited to #{@sale.user.email}."
    else
      flash[:alert] = "Sale is not pending and cannot be approved."
    end
    redirect_to admin_sales_path
  end

  def reject
    if @sale.pending?
      @sale.update!(status: :rejected, approved_by: current_user.id, approved_at: Time.current)
      flash[:notice] = "Sale rejected."
    else
      flash[:alert] = "Sale is not pending and cannot be rejected."
    end
    redirect_to admin_sales_path
  end

  def show
  end

  private

  def set_sale
    @sale = Sale.find(params[:id])
  end
end
