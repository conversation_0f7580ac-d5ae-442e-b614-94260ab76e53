class Admin::UsersController < Admin::BaseController
  before_action :set_user, only: [:show, :edit, :update, :activate, :deactivate]

  def index
    @q = User.includes(:store, :wallet).ransack(params[:q])
    @pagy, @users = pagy(@q.result(distinct: true).order(created_at: :desc), items: 20)
  end

  def show
    @recent_sales = @user.sales.includes(:product).order(created_at: :desc).limit(10)
    @recent_orders = @user.orders.includes(:product).order(created_at: :desc).limit(10)
  end

  def edit
  end

  def update
    result = @user.update(user_params)
    if result
      flash[:notice] = "User updated successfully."
      redirect_to admin_user_path(@user)
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def activate
    @user.update!(status: :active)
    flash[:notice] = "User activated successfully."
    redirect_to admin_users_path
  end

  def deactivate
    @user.update!(status: :inactive)
    flash[:notice] = "User deactivated successfully."
    redirect_to admin_users_path
  end

  private

  def set_user
    @user = User.find(params[:id])
  end

  def user_params
    params.require(:user).permit(:email, :role, :status, :store_id)
  end
end
