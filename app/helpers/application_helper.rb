module ApplicationHelper
  # Returns an image tag for the product's image or a fallback if missing
  def product_image_tag(product, options = {})
    if product.image.attached?
      image_tag product.image, {alt: product.name, class: "w-full h-48 object-cover mb-2"}.merge(options)
    else
      image_tag "image-missing.svg", {alt: "Image missing", class: "w-full h-48 object-cover mb-2 opacity-60"}.merge(options)
    end
  end
end
