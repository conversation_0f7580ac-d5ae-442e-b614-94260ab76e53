# == Schema Information
#
# Table name: stores
#
#  id             :bigint           not null, primary key
#  name           :string           not null
#  phone_number   :string           not null
#  status         :integer          default("requested"), not null
#  created_at     :datetime         not null
#  updated_at     :datetime         not null
#  brand_id       :bigint
#  store_chain_id :bigint
#
# Indexes
#
#  index_stores_on_brand_id        (brand_id)
#  index_stores_on_status          (status)
#  index_stores_on_store_chain_id  (store_chain_id)
#
# Foreign Keys
#
#  fk_rails_...  (brand_id => brands.id)
#  fk_rails_...  (store_chain_id => store_chains.id)
#
class Store < ApplicationRecord
  include PhoneNumberNormalizable

  belongs_to :store_chain, optional: true
  belongs_to :brand
  has_many :users, dependent: :nullify
  has_one :address, as: :addressable, dependent: :destroy
  enum :status, {requested: 0, active: 1, inactive: 2}

  validates :name, presence: true
  validates :status, presence: true

  # Ransack configuration
  def self.ransackable_attributes(auth_object = nil)
    %w[name phone_number status created_at updated_at] + _ransackers.keys
  end

  def self.ransackable_associations(auth_object = nil)
    %w[brand address]
  end

  # Custom ransackers for enhanced search functionality
  # Note: For complex association searches, it's better to use Ransack's built-in
  # association search syntax like: address_city_cont, address_state_name_cont

  # Simple ransacker for store status as text
  ransacker :status_text do |parent|
    Arel::Nodes::Case.new(parent.table[:status])
      .when(0).then("Requested")
      .when(1).then("Active")
      .when(2).then("Inactive")
      .else("Unknown")
  end
end
