# == Schema Information
#
# Table name: users
#
#  id                     :bigint           not null, primary key
#  confirmation_sent_at   :datetime
#  confirmation_token     :string
#  confirmed_at           :datetime
#  current_sign_in_at     :datetime
#  current_sign_in_ip     :string
#  email                  :string           default(""), not null
#  encrypted_password     :string           default(""), not null
#  failed_attempts        :integer          default(0), not null
#  last_sign_in_at        :datetime
#  last_sign_in_ip        :string
#  locked_at              :datetime
#  remember_created_at    :datetime
#  reset_password_sent_at :datetime
#  reset_password_token   :string
#  role                   :integer          default("regular"), not null
#  sign_in_count          :integer          default(0), not null
#  status                 :integer          default("inactive"), not null
#  unconfirmed_email      :string
#  unlock_token           :string
#  created_at             :datetime         not null
#  updated_at             :datetime         not null
#  store_id               :bigint
#
# Indexes
#
#  index_users_on_confirmation_token    (confirmation_token) UNIQUE
#  index_users_on_email                 (email) UNIQUE
#  index_users_on_reset_password_token  (reset_password_token) UNIQUE
#  index_users_on_status                (status)
#  index_users_on_store_id              (store_id)
#  index_users_on_unlock_token          (unlock_token) UNIQUE
#
# Foreign Keys
#
#  fk_rails_...  (store_id => stores.id)
#
class User < ApplicationRecord
  has_many :orders, dependent: :nullify
  has_many :sales, dependent: :nullify
  belongs_to :store, optional: true
  has_one :address, as: :addressable, dependent: :destroy
  accepts_nested_attributes_for :address
  # Regions where this user is the admin
  has_many :regions, foreign_key: :admin_user_id, dependent: :nullify
  has_one :cart, dependent: :destroy
  # Include default devise modules. Others available are:
  # :timeoutable, :omniauthable
  devise :database_authenticatable, :registerable,
    :recoverable, :rememberable, :validatable,
    :trackable, :confirmable, :lockable

  enum :role, {regular: 0, admin: 1, super_admin: 2}, default: :regular
  enum :status, {active: 0, inactive: 1, deleted: 2}, default: :inactive

  validates :email, presence: true, uniqueness: true
  validates :role, presence: true
  validates :store_id, presence: true, on: :create
  validates :status, presence: true

  has_one :wallet, dependent: :destroy
  after_create :create_wallet

  # Only allow sign in if email is confirmed, store is active, and user is active
  def active_for_authentication?
    super && confirmed? && store&.active? && active?
  end

  def inactive_message
    return :unconfirmed unless confirmed?
    return :inactive_store unless store&.active?
    return :inactive unless active?
    super
  end

  # Ransack configuration for admin search
  def self.ransackable_attributes(auth_object = nil)
    %w[created_at email id role status updated_at store_id sign_in_count last_sign_in_at confirmed_at]
  end

  def self.ransackable_associations(auth_object = nil)
    %w[store wallet sales orders address]
  end

  private

  def create_wallet
    Wallet.create!(user: self)
  end
end
