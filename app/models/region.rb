# == Schema Information
#
# Table name: regions
#
#  id            :bigint           not null, primary key
#  name          :string           not null
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#  admin_user_id :bigint           not null
#
# Indexes
#
#  index_regions_on_admin_user_id  (admin_user_id)
#  index_regions_on_name           (name) UNIQUE
#
# Foreign Keys
#
#  fk_rails_...  (admin_user_id => users.id)
#
class Region < ApplicationRecord
  belongs_to :admin_user, class_name: "User"
  has_many :states, dependent: :destroy

  validates :name, presence: true, uniqueness: true
end
