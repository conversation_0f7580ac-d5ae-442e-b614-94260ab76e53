# == Schema Information
#
# Table name: categories
#
#  id         :bigint           not null, primary key
#  name       :string           not null
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  brand_id   :bigint           not null
#
# Indexes
#
#  index_categories_on_brand_id           (brand_id)
#  index_categories_on_name_and_brand_id  (name,brand_id) UNIQUE
#
# Foreign Keys
#
#  fk_rails_...  (brand_id => brands.id)
#
class Category < ApplicationRecord
  belongs_to :brand
  has_many :products, dependent: :restrict_with_exception

  validates :name, presence: true, uniqueness: {scope: :brand_id}
end
