# == Schema Information
#
# Table name: brands
#
#  id         :bigint           not null, primary key
#  name       :string           not null
#  created_at :datetime         not null
#  updated_at :datetime         not null
#
# Indexes
#
#  index_brands_on_name  (name) UNIQUE
#
class Brand < ApplicationRecord
  has_many :categories, dependent: :restrict_with_exception
  has_many :stores, dependent: :restrict_with_exception

  validates :name, presence: true, uniqueness: true
end
