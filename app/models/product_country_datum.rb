# == Schema Information
#
# Table name: product_country_data
#
#  id            :bigint           not null, primary key
#  msrp          :decimal(10, 2)
#  points_cost   :integer
#  points_earned :integer
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#  country_id    :bigint           not null
#  product_id    :bigint           not null
#
# Indexes
#
#  index_product_country_data_on_country_id                 (country_id)
#  index_product_country_data_on_product_id                 (product_id)
#  index_product_country_data_on_product_id_and_country_id  (product_id,country_id) UNIQUE
#
# Foreign Keys
#
#  fk_rails_...  (country_id => countries.id)
#  fk_rails_...  (product_id => products.id)
#
class ProductCountryDatum < ApplicationRecord
  belongs_to :product
  belongs_to :country

  validates :country_id, presence: true
  validates :msrp, numericality: {greater_than_or_equal_to: 0}, allow_nil: true
  validates :points_earned, numericality: {only_integer: true, greater_than_or_equal_to: 0}, allow_nil: true
  validates :points_cost, numericality: {only_integer: true, greater_than_or_equal_to: 0}, allow_nil: true
  validates :product_id, uniqueness: {scope: :country_id}
end
