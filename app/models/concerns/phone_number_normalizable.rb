# frozen_string_literal: true

module PhoneNumberNormalizable
  extend ActiveSupport::Concern

  PHONE_REGEX = /\A\(?\d{3}\)?[\s.-]?\d{3}[\s.-]?\d{4}\z/

  included do
    before_validation :normalize_phone_number!
    validates :phone_number, presence: true, format: {with: PHONE_REGEX, message: "must be a valid 10-digit North American number"}
  end

  private

  def normalize_phone_number!
    return if phone_number.blank?
    digits = phone_number.gsub(/\D/, "")
    if digits.length == 10
      self.phone_number = "(#{digits[0..2]}) #{digits[3..5]}-#{digits[6..9]}"
    end
  end
end
