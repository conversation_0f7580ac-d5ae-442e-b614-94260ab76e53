# == Schema Information
#
# Table name: countries
#
#  id         :bigint           not null, primary key
#  code       :string(2)        not null
#  name       :string           not null
#  created_at :datetime         not null
#  updated_at :datetime         not null
#
# Indexes
#
#  index_countries_on_code  (code) UNIQUE
#  index_countries_on_name  (name) UNIQUE
#
class Country < ApplicationRecord
  validates :code, presence: true, uniqueness: true, length: {is: 2}
  validates :name, presence: true, uniqueness: true

  has_many :addresses
end
