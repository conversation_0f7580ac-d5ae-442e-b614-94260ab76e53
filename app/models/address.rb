# == Schema Information
#
# Table name: addresses
#
#  id               :bigint           not null, primary key
#  addressable_type :string           not null
#  city             :string           not null
#  postal_code      :string           not null
#  street           :string           not null
#  created_at       :datetime         not null
#  updated_at       :datetime         not null
#  addressable_id   :bigint           not null
#  country_id       :bigint           not null
#  state_id         :bigint
#
# Indexes
#
#  index_addresses_on_addressable                          (addressable_type,addressable_id)
#  index_addresses_on_addressable_type_and_addressable_id  (addressable_type,addressable_id)
#  index_addresses_on_country_id                           (country_id)
#  index_addresses_on_state_id                             (state_id)
#
# Foreign Keys
#
#  fk_rails_...  (country_id => countries.id)
#  fk_rails_...  (state_id => states.id)
#
class Address < ApplicationRecord
  belongs_to :addressable, polymorphic: true
  belongs_to :state, optional: false
  belongs_to :country

  validates :street, :city, :postal_code, presence: true
  validates :country_id, :state_id, presence: true

  # Ransack configuration for admin search
  def self.ransackable_attributes(auth_object = nil)
    %w[street city postal_code created_at updated_at id country_id state_id]
  end

  def self.ransackable_associations(auth_object = nil)
    %w[country state addressable]
  end
end
