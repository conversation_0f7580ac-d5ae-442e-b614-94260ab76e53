# == Schema Information
#
# Table name: sales
#
#  id            :bigint           not null, primary key
#  approved_at   :datetime
#  approved_by   :integer
#  notes         :text
#  points        :integer          not null
#  serial_number :string           not null
#  sold_at       :datetime         not null
#  status        :integer          default("pending"), not null
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#  product_id    :bigint           not null
#  user_id       :bigint           not null
#
# Indexes
#
#  index_sales_on_approved_by    (approved_by)
#  index_sales_on_product_id     (product_id)
#  index_sales_on_serial_number  (serial_number) UNIQUE
#  index_sales_on_user_id        (user_id)
#
# Foreign Keys
#
#  fk_rails_...  (product_id => products.id)
#  fk_rails_...  (user_id => users.id)
#
class Sale < ApplicationRecord
  belongs_to :user
  belongs_to :product
  belongs_to :approved_by_admin, class_name: "User", foreign_key: "approved_by", optional: true

  enum :status, {pending: 0, approved: 1, rejected: 2}

  validates :serial_number, presence: true, uniqueness: true, length: {minimum: 8}
  validates :user, :product, :status, :sold_at, :points, presence: true
  validates :points, numericality: {only_integer: true, greater_than_or_equal_to: 0}
  validates :sold_at, comparison: {less_than_or_equal_to: ->(_) { Time.current }, message: "cannot be in the future"}

  before_validation :set_points_from_product_country_datum, if: -> { user&.address&.country_id && product_id && points.blank? }

  # Ransacker for status to allow string-based search
  ransacker :status, formatter: proc { |v| statuses[v] } do |parent|
    parent.table[:status]
  end

  # Ransack configuration for admin search
  def self.ransackable_attributes(auth_object = nil)
    %w[approved_at approved_by created_at id notes points product_id serial_number sold_at status updated_at user_id]
  end

  def self.ransackable_associations(auth_object = nil)
    %w[user product approved_by_admin]
  end

  private

  def set_points_from_product_country_datum
    country_id = user.address.country_id
    datum = ProductCountryDatum.find_by(product_id: product_id, country_id: country_id)
    self.points = datum&.points_earned || 0
  end
end
