# == Schema Information
#
# Table name: wallets
#
#  id         :bigint           not null, primary key
#  points     :integer          default(0), not null
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  user_id    :bigint           not null
#
# Indexes
#
#  index_wallets_on_user_id  (user_id) UNIQUE
#
# Foreign Keys
#
#  fk_rails_...  (user_id => users.id)
#

class Wallet < ApplicationRecord
  include PublicActivity::Model
  belongs_to :user

  validates :points, numericality: {greater_than_or_equal_to: 0}

  # Credits points to the wallet, optionally linked to an order or sale
  def credit(amount, context: nil)
    raise ArgumentError, "Amount must be positive" unless amount.positive?
    increment!(:points, amount)
    create_activity!(:credit, amount:, context:)
  end

  # Debits points from the wallet, optionally linked to an order or sale
  def debit(amount, context: nil)
    raise ArgumentError, "Amount must be positive" unless amount.positive?
    raise StandardError, "Insufficient points" if points < amount
    decrement!(:points, amount)
    create_activity!(:debit, amount:, context:)
  end

  private

  # context: an Order or Sale instance (or nil)
  def create_activity!(action, amount:, context: nil)
    owner = user
    activity_params = {
      owner:,
      recipient: context,
      parameters: {
        amount:,
        context_type: context&.class&.name,
        context_id: context&.id
      }
    }
    create_activity(action, **activity_params)
  end
end
