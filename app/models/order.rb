# == Schema Information
#
# Table name: orders
#
#  id               :bigint           not null, primary key
#  points           :integer          not null
#  shipping_address :string
#  shipping_type    :string           not null
#  status           :integer          default("pending"), not null
#  total            :decimal(10, 2)   default(0.0), not null
#  created_at       :datetime         not null
#  updated_at       :datetime         not null
#  user_id          :bigint           not null
#
# Indexes
#
#  index_orders_on_user_id  (user_id)
#
# Foreign Keys
#
#  fk_rails_...  (user_id => users.id)
#
class Order < ApplicationRecord
  belongs_to :user
  has_many :line_items, dependent: :destroy
  has_many :products, through: :line_items

  enum :status, {pending: 0, approved: 1, rejected: 2}

  validates :shipping_type, presence: true
  validates :points, presence: true, numericality: {greater_than: 0}
end
