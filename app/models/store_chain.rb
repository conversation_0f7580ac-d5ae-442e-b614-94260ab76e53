# == Schema Information
#
# Table name: store_chains
#
#  id         :bigint           not null, primary key
#  name       :string           not null
#  created_at :datetime         not null
#  updated_at :datetime         not null
#
# Indexes
#
#  index_store_chains_on_name  (name) UNIQUE
#
class StoreChain < ApplicationRecord
  has_many :stores, dependent: :nullify

  validates :name, presence: true, uniqueness: true
end
