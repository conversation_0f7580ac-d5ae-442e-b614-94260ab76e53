import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["value", "field", "product"]

  connect() {
    if (this.hasProductTarget) {
      this.productTarget.addEventListener('change', this.updatePoints.bind(this))
    }
  }

  async updatePoints() {
    const productId = this.hasProductTarget ? this.productTarget.value : undefined
    console.debug('[Stimulus] updatePoints called, productId:', productId)
    if (!productId) {
      this.valueTarget.textContent = '-'
      return
    }
    try {
      const response = await fetch(`/sales/points?product_id=${productId}`)
      console.debug('[Stimulus] fetch response:', response)
      if (response.ok) {
        const data = await response.json()
        console.debug('[Stimulus] response data:', data)
        this.valueTarget.textContent = data.points
        this.fieldTarget.value = data.points
      } else {
        console.error('[Stimulus] fetch error:', response.statusText)
        this.valueTarget.textContent = '-'
      }
    } catch (e) {
      console.error('[Stimulus] fetch error:', e)
      this.valueTarget.textContent = '-'
    }
  }
}
