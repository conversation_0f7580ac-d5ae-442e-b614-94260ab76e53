
// Mobile-first, offline-capable service worker for ZeisspointsRails8
const CACHE_NAME = 'zeisspoints-pwa-v1';
const OFFLINE_URL = '/offline.html';
const ASSETS_TO_CACHE = [
  '/',
  '/icon.png',
  '/icon.svg',
  '/assets/tailwind/application.css',
  '/manifest.json',
  OFFLINE_URL
];

self.addEventListener('install', event => {
  event.waitUntil(
    caches.open(CACHE_NAME).then(cache => cache.addAll(ASSETS_TO_CACHE))
  );
  self.skipWaiting();
});

self.addEventListener('activate', event => {
  event.waitUntil(
    caches.keys().then(keys =>
      Promise.all(keys.filter(key => key !== CACHE_NAME).map(key => caches.delete(key)))
    )
  );
  self.clients.claim();
});

self.addEventListener('fetch', event => {
  if (event.request.method !== 'GET') return;
  event.respondWith(
    caches.match(event.request).then(response => {
      return response || fetch(event.request).catch(() => {
        if (event.request.mode === 'navigate') {
          return caches.match(OFFLINE_URL);
        }
      });
    })
  );
});

// Uncomment to enable push notifications
// self.addEventListener('push', async event => {
//   const { title, options } = await event.data.json();
//   event.waitUntil(self.registration.showNotification(title, options));
// });

// self.addEventListener('notificationclick', function(event) {
//   event.notification.close();
//   event.waitUntil(
//     clients.matchAll({ type: 'window' }).then(clientList => {
//       for (let i = 0; i < clientList.length; i++) {
//         let client = clientList[i];
//         let clientPath = (new URL(client.url)).pathname;
//         if (clientPath == event.notification.data.path && 'focus' in client) {
//           return client.focus();
//         }
//       }
//       if (clients.openWindow) {
//         return clients.openWindow(event.notification.data.path);
//       }
//     })
//   );
// });
