<!-- Flash Messages -->
<% if flash[:notice] %>
  <div class="notice bg-green-50 border border-green-200 text-green-800 rounded-xl p-4 mb-4">
    <%= flash[:notice] %>
  </div>
<% end %>
<% if flash[:alert] %>
  <div class="alert bg-red-50 border border-red-200 text-red-800 rounded-xl p-4 mb-4">
    <%= flash[:alert] %>
  </div>
<% end %>
<!-- Sale Form Content -->
<div class="px-4 py-6">

  <!-- Error Messages -->
  <% if @sale&.errors&.any? %>
    <div class="mb-6 p-4 bg-red-50 border border-red-200 rounded-xl">
      <div class="flex items-center mb-2">
        <svg class="w-5 h-5 text-red-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
        </svg>
        <h3 class="text-sm font-medium text-red-800">Please fix the following errors:</h3>
      </div>
      <ul class="text-sm text-red-700 space-y-1">
        <% @sale.errors.full_messages.each do |message| %>
          <li>&bull; <%= message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <!-- Form Card -->
  <div class="bg-white rounded-2xl shadow-sm border border-gray-200 p-6">

    <%= form_with model: @sale, url: sales_path, method: :post, local: true,
        class: "space-y-6" do |f| %>

      <!-- Product Selection and Points Display (Stimulus Controller Wrapper) -->
      <div data-controller="points">
        <div class="space-y-2">
          <%= f.label :product_id, 'Product',
              class: "block text-sm font-semibold text-gray-700" %>
          <%= f.collection_select :product_id, @products, :id, :name,
              { prompt: 'Select a product' },
              { class: "w-full px-4 py-3 rounded-xl border border-gray-300
                       focus:ring-2 focus:ring-zeiss-500 focus:border-zeiss-500
                       transition-colors text-base sm:text-sm",
                data: { action: "change->points#updatePoints", points_target: "product" } } %>
        </div>

        <!-- Points Display -->
        <div class="space-y-2" id="points_display">
          <label class="block text-sm font-semibold text-gray-700">Points Earned</label>
          <div class="w-full px-4 py-3 rounded-xl border border-gray-200 bg-gray-50
                      flex items-center justify-between">
            <span data-points-target="value" class="text-base font-medium text-gray-900">
              <%= @points || '-' %>
            </span>
            <div class="flex items-center text-zeiss-600">
              <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
              </svg>
              <span class="text-sm font-medium">points</span>
            </div>
          </div>
          <%= f.hidden_field :points, value: @points, data: { points_target: "field" } %>
        </div>
      </div>

      <!-- Serial Number with Barcode Scanner -->
      <div class="space-y-2">
        <%= f.label :serial_number, 'Serial Number',
            class: "block text-sm font-semibold text-gray-700" %>
        <div class="relative" style="position: relative;">
          <%= f.text_field :serial_number,
              placeholder: "Enter or scan serial number",
              required: true,
              class: "w-full px-4 py-3 pr-12 rounded-xl border border-gray-300
                     focus:ring-2 focus:ring-zeiss-500 focus:border-zeiss-500
                     transition-colors text-base sm:text-sm
                     placeholder:text-gray-400",
              data: {
                controller: "barcode",
                barcode_target: "input",
                barcode_readers_value: "code_128",
                barcode_frequency_value: "10",
                barcode_half_sample_value: "true",
                barcode_patch_size_value: "medium"
              } %>
          <div class="absolute inset-y-0 right-0 flex items-center pr-3">
            <button type="button" class="absolute inset-y-0 right-0 flex items-center pr-3 p-1 text-gray-400 hover:text-gray-600 transition-colors" data-barcode-btn="" data-action="click->barcode#startScanner" title="Scan barcode" aria-label="Scan barcode">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h2M4 4h5m3 0h6m-9 4h2m3 0h2M4 20h5m3 0h6m-9-4h2m3 0h2"></path>
              </svg>
            </button>
          </div>
        </div>
        <p class="text-xs text-gray-500">Tap the barcode icon to scan</p>
      </div>

      <!-- Sale Date -->
      <div class="space-y-2">
        <%= f.label :sold_at, 'Sale Date',
            class: "block text-sm font-semibold text-gray-700" %>
        <%= f.date_field :sold_at,
            value: Date.current,
            max: Date.current,
            class: "w-full px-4 py-3 rounded-xl border border-gray-300
                   focus:ring-2 focus:ring-zeiss-500 focus:border-zeiss-500
                   transition-colors text-base sm:text-sm" %>
        <p class="text-xs text-gray-500">Sales can only be recorded for today or past dates</p>
      </div>

      <!-- Notes (Optional) -->
      <div class="space-y-2">
        <%= f.label :notes, 'Notes (Optional)',
            class: "block text-sm font-semibold text-gray-700" %>
        <%= f.text_area :notes,
            rows: 3,
            placeholder: "Add any additional notes about this sale...",
            class: "w-full px-4 py-3 rounded-xl border border-gray-300
                   focus:ring-2 focus:ring-zeiss-500 focus:border-zeiss-500
                   transition-colors text-base sm:text-sm resize-none
                   placeholder:text-gray-400" %>
      </div>

      <!-- Submit Button -->
      <div class="pt-4">
        <%= f.submit 'Record Sale',
            class: "w-full py-4 px-4 bg-zeiss-600 hover:bg-zeiss-700
                   text-white font-semibold rounded-xl
                   transition-colors duration-200
                   focus:ring-2 focus:ring-zeiss-500 focus:ring-offset-2
                   text-base shadow-sm
                   active:bg-zeiss-800" %>
      </div>
    <% end %>
  </div>

  <!-- Help Section -->
  <div class="mt-6 bg-blue-50 border border-blue-200 rounded-xl p-4">
    <div class="flex items-start">
      <svg class="w-5 h-5 text-blue-600 mt-0.5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
      </svg>
      <div>
        <h4 class="text-sm font-semibold text-blue-900 mb-1">Recording Sales</h4>
        <p class="text-sm text-blue-800">
          Select the product, enter the serial number (or scan the barcode), and confirm the sale date.
          Points will be automatically calculated and added to your account once approved.
        </p>
      </div>
    </div>
  </div>
</div>
