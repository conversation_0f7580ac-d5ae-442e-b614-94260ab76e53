<h1 class="text-2xl font-bold mb-4">Checkout</h1>
<div class="max-w-2xl mx-auto bg-white p-6 rounded shadow">
  <h2 class="text-lg font-semibold mb-2">Order Summary</h2>
  <table class="min-w-full divide-y divide-gray-200 mb-4">
    <thead>
      <tr>
        <th class="px-4 py-2">Product</th>
        <th class="px-4 py-2">Quantity</th>
        <th class="px-4 py-2">Price</th>
        <th class="px-4 py-2">Total</th>
      </tr>
    </thead>
    <tbody>
      <% @cart.line_items.includes(:product).each do |item| %>
        <tr>
          <td class="px-4 py-2"><%= item.product.name %></td>
          <td class="px-4 py-2"><%= item.quantity %></td>
          <td class="px-4 py-2"><%= number_to_currency(item.price) %></td>
          <td class="px-4 py-2"><%= number_to_currency(item.total_price) %></td>
        </tr>
      <% end %>
    </tbody>
  </table>
  <div class="mb-4 text-lg font-semibold">Total: <%= number_to_currency(@cart.total_price) %></div>
  <%= form_with model: @order, url: orders_path, local: true do |f| %>
    <div class="mb-4">
      <%= f.label :shipping_type, 'Ship to:' %><br>
      <%= f.select :shipping_type, [['My Address', 'user'], ['My Store', 'store']], {}, class: 'form-select' %>
    </div>
    <div class="mb-4">
      <%= f.label :shipping_address, 'Shipping Address (if shipping to you):' %>
      <%= f.text_field :shipping_address, class: 'form-input', placeholder: 'Enter address if shipping to you' %>
    </div>
    <%= f.submit 'Place Order', class: 'bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700' %>
  <% end %>
</div>
