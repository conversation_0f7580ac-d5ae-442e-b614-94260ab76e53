<!-- Top Navigation Bar -->
<nav class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-40">
  <div class="px-4 py-3 sm:px-6 lg:px-8">
    <div class="flex items-center justify-between">
      <!-- Left Side: Back Button or Logo -->
      <div class="flex items-center space-x-3">
        <% if local_assigns[:show_back_button] %>
          <%= link_to (local_assigns[:back_path] || root_path),
              class: "p-2 -ml-2 rounded-full hover:bg-gray-100 transition-colors" do %>
            <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
          <% end %>
        <% else %>
          <%= image_tag 'zeisslogo.png', alt: '<PERSON>eiss Logo', class: 'h-8 w-auto' %>
        <% end %>

        <div>
          <h1 class="text-lg font-bold text-gray-900">
            <%= local_assigns[:title] || 'ZeissPoints' %>
          </h1>
          <% if local_assigns[:subtitle] %>
            <p class="text-xs text-gray-500"><%= local_assigns[:subtitle] %></p>
          <% end %>
        </div>
      </div>

      <!-- Right Side: Points and Profile -->
      <div class="flex items-center space-x-2">
        <!-- Points Display -->
        <div class="bg-zeiss-50 px-3 py-1 rounded-full">
          <span class="text-sm font-semibold text-zeiss-700">
            <%= current_user.wallet&.points || 0 %> pts
          </span>
        </div>

        <!-- Admin Access (if admin) -->
        <% if current_user.admin? || current_user.super_admin? %>
          <%= link_to admin_root_path,
              class: "p-2 rounded-full bg-red-100 hover:bg-red-200 transition-colors",
              title: "Admin Dashboard" do %>
            <svg class="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
            </svg>
          <% end %>
        <% end %>

        <!-- Profile Menu -->
        <div class="relative">
          <button class="p-2 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors">
            <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>
</nav>