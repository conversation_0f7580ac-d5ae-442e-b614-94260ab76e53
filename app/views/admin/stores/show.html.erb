<!-- Admin Store Detail View -->
<div class="space-y-6">
  <!-- <PERSON> Header -->
  <div class="md:flex md:items-center md:justify-between">
    <div class="flex-1 min-w-0">
      <nav class="flex" aria-label="Breadcrumb">
        <ol class="flex items-center space-x-4">
          <li>
            <%= link_to admin_stores_path, class: "text-gray-400 hover:text-gray-500" do %>
              <span>Stores</span>
            <% end %>
          </li>
          <li>
            <div class="flex items-center">
              <svg class="flex-shrink-0 h-5 w-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
              </svg>
              <span class="ml-4 text-sm font-medium text-gray-500"><%= @store.name %></span>
            </div>
          </li>
        </ol>
      </nav>
      <h2 class="mt-2 text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
        Store Details
      </h2>
    </div>
    <div class="mt-4 flex md:mt-0 md:ml-4">
      <%= link_to edit_admin_store_path(@store),
          class: "inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" do %>
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
        </svg>
        Edit Store
      <% end %>
    </div>
  </div>

  <!-- Store Overview Cards -->
  <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
    <!-- Store Info Card -->
    <div class="bg-white shadow-sm rounded-lg border border-gray-200 p-6">
      <div class="flex items-center">
        <div class="flex-shrink-0 h-16 w-16">
          <div class="h-16 w-16 rounded-full bg-blue-100 flex items-center justify-center">
            <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
            </svg>
          </div>
        </div>
        <div class="ml-4">
          <h3 class="text-lg font-medium text-gray-900"><%= @store.name %></h3>
          <div class="flex items-center space-x-2 mt-1">
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                         <%= @store.status == 'active' ? 'bg-green-100 text-green-800' :
                             @store.status == 'requested' ? 'bg-yellow-100 text-yellow-800' :
                             'bg-red-100 text-red-800' %>">
              <%= @store.status.humanize %>
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- Users Card -->
    <div class="bg-white shadow-sm rounded-lg border border-gray-200 p-6">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
            </svg>
          </div>
        </div>
        <div class="ml-4">
          <p class="text-sm font-medium text-gray-500">Assigned Users</p>
          <p class="text-2xl font-bold text-gray-900"><%= @users.count %></p>
        </div>
      </div>
    </div>

    <!-- Sales Activity Card -->
    <div class="bg-white shadow-sm rounded-lg border border-gray-200 p-6">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
            <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
            </svg>
          </div>
        </div>
        <div class="ml-4">
          <p class="text-sm font-medium text-gray-500">Recent Sales</p>
          <p class="text-2xl font-bold text-gray-900"><%= @recent_sales.count %></p>
          <p class="text-xs text-gray-500">Last 30 days</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Detailed Information -->
  <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
    <!-- Store Details -->
    <div class="bg-white shadow-sm rounded-lg border border-gray-200">
      <div class="px-6 py-5 border-b border-gray-200">
        <h3 class="text-lg leading-6 font-semibold text-gray-900">Store Information</h3>
      </div>
      <div class="px-6 py-5">
        <dl class="space-y-4">
          <div>
            <dt class="text-sm font-medium text-gray-500">Store Name</dt>
            <dd class="mt-1 text-sm text-gray-900"><%= @store.name %></dd>
          </div>
          <div>
            <dt class="text-sm font-medium text-gray-500">Phone Number</dt>
            <dd class="mt-1 text-sm text-gray-900"><%= @store.phone_number || 'Not provided' %></dd>
          </div>
          <div>
            <dt class="text-sm font-medium text-gray-500">Status</dt>
            <dd class="mt-1 text-sm text-gray-900"><%= @store.status.humanize %></dd>
          </div>
          <div>
            <dt class="text-sm font-medium text-gray-500">Brand</dt>
            <dd class="mt-1 text-sm text-gray-900">
              <% if @store.brand %>
                <%= @store.brand.name %>
              <% else %>
                <span class="text-gray-400">No brand</span>
              <% end %>
            </dd>
          </div>
          <div>
            <dt class="text-sm font-medium text-gray-500">Added</dt>
            <dd class="mt-1 text-sm text-gray-900"><%= @store.created_at.strftime('%B %d, %Y at %I:%M %p') %></dd>
          </div>
          <div>
            <dt class="text-sm font-medium text-gray-500">Last Updated</dt>
            <dd class="mt-1 text-sm text-gray-900"><%= @store.updated_at.strftime('%B %d, %Y at %I:%M %p') %></dd>
          </div>
        </dl>
      </div>
    </div>

    <!-- Address Information -->
    <div class="bg-white shadow-sm rounded-lg border border-gray-200">
      <div class="px-6 py-5 border-b border-gray-200">
        <h3 class="text-lg leading-6 font-semibold text-gray-900">Address</h3>
      </div>
      <div class="px-6 py-5">
        <% if @store.address %>
          <dl class="space-y-4">
            <div>
              <dt class="text-sm font-medium text-gray-500">Street Address</dt>
              <dd class="mt-1 text-sm text-gray-900"><%= @store.address.street %></dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500">City</dt>
              <dd class="mt-1 text-sm text-gray-900"><%= @store.address.city %></dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500">State</dt>
              <dd class="mt-1 text-sm text-gray-900"><%= @store.address.state&.name %></dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500">Postal Code</dt>
              <dd class="mt-1 text-sm text-gray-900"><%= @store.address.postal_code %></dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500">Country</dt>
              <dd class="mt-1 text-sm text-gray-900"><%= @store.address.country&.name %></dd>
            </div>
          </dl>
        <% else %>
          <div class="text-center py-8">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No address provided</h3>
            <p class="mt-1 text-sm text-gray-500">This store doesn't have an address on file.</p>
          </div>
        <% end %>
      </div>
    </div>
  </div>

  <!-- Users and Activity -->
  <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
    <!-- Assigned Users -->
    <div class="bg-white shadow-sm rounded-lg border border-gray-200">
      <div class="px-6 py-5 border-b border-gray-200">
        <h3 class="text-lg leading-6 font-semibold text-gray-900">Assigned Users</h3>
      </div>
      <div class="px-6 py-5">
        <% if @users.any? %>
          <div class="space-y-4">
            <% @users.each do |user| %>
              <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg border border-gray-100">
                <div class="flex items-center">
                  <div class="flex-shrink-0 h-8 w-8">
                    <div class="h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center">
                      <span class="text-xs font-medium text-gray-700">
                        <%= user.email.first.upcase %>
                      </span>
                    </div>
                  </div>
                  <div class="ml-3">
                    <p class="text-sm font-semibold text-gray-900"><%= user.email %></p>
                    <div class="flex items-center space-x-2 mt-1">
                      <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium
                                   <%= user.role == 'admin' ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-800' %>">
                        <%= user.role.humanize %>
                      </span>
                      <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium
                                   <%= user.status == 'active' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' %>">
                        <%= user.status.humanize %>
                      </span>
                    </div>
                  </div>
                </div>
                <div class="text-right">
                  <p class="text-sm font-medium text-blue-600"><%= user.wallet&.points || 0 %> pts</p>
                  <%= link_to admin_user_path(user),
                      class: "text-xs text-blue-600 hover:text-blue-800" do %>
                    View →
                  <% end %>
                </div>
              </div>
            <% end %>
          </div>
        <% else %>
          <div class="text-center py-8">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No users assigned</h3>
            <p class="mt-1 text-sm text-gray-500">No users are currently assigned to this store.</p>
          </div>
        <% end %>
      </div>
    </div>

    <!-- Recent Sales -->
    <div class="bg-white shadow-sm rounded-lg border border-gray-200">
      <div class="px-6 py-5 border-b border-gray-200">
        <h3 class="text-lg leading-6 font-semibold text-gray-900">Recent Sales</h3>
      </div>
      <div class="px-6 py-5">
        <% if @recent_sales.any? %>
          <div class="space-y-4">
            <% @recent_sales.each do |sale| %>
              <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg border border-gray-100">
                <div class="flex-1">
                  <p class="text-sm font-semibold text-gray-900"><%= sale.product.name %></p>
                  <p class="text-xs text-gray-500 mt-1">
                    By <%= sale.user.email %> • <%= sale.serial_number %>
                  </p>
                  <p class="text-xs text-gray-500">
                    <%= sale.created_at.strftime('%b %d, %Y') %>
                  </p>
                </div>
                <div class="text-right ml-4">
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                               <%= sale.status == 'approved' ? 'bg-green-100 text-green-800' :
                                   sale.status == 'pending' ? 'bg-yellow-100 text-yellow-800' :
                                   'bg-red-100 text-red-800' %>">
                    <%= sale.status.capitalize %>
                  </span>
                  <p class="text-sm font-bold text-blue-600 mt-1">
                    <%= sale.points %> pts
                  </p>
                </div>
              </div>
            <% end %>
          </div>
        <% else %>
          <div class="text-center py-8">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No sales yet</h3>
            <p class="mt-1 text-sm text-gray-500">No sales have been recorded for this store.</p>
          </div>
        <% end %>
      </div>
    </div>
  </div>
</div>