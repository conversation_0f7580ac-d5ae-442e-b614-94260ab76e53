<!-- Admin Store Edit View -->
<div class="space-y-6">
  <!-- <PERSON> Header -->
  <div class="md:flex md:items-center md:justify-between">
    <div class="flex-1 min-w-0">
      <nav class="flex" aria-label="Breadcrumb">
        <ol class="flex items-center space-x-4">
          <li>
            <%= link_to admin_stores_path, class: "text-gray-400 hover:text-gray-500" do %>
              <span>Stores</span>
            <% end %>
          </li>
          <li>
            <div class="flex items-center">
              <svg class="flex-shrink-0 h-5 w-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
              </svg>
              <%= link_to admin_store_path(@store), class: "ml-4 text-gray-400 hover:text-gray-500" do %>
                <span><%= @store.name %></span>
              <% end %>
            </div>
          </li>
          <li>
            <div class="flex items-center">
              <svg class="flex-shrink-0 h-5 w-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
              </svg>
              <span class="ml-4 text-sm font-medium text-gray-500">Edit</span>
            </div>
          </li>
        </ol>
      </nav>
      <h2 class="mt-2 text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
        Edit Store
      </h2>
      <p class="mt-1 text-sm text-gray-500">
        Update store information, status, and settings
      </p>
    </div>
  </div>

  <!-- Error Messages -->
  <% if @store.errors.any? %>
    <div class="bg-red-50 border border-red-200 rounded-md p-4">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-red-800">
            Please fix the following errors:
          </h3>
          <div class="mt-2 text-sm text-red-700">
            <ul class="list-disc pl-5 space-y-1">
              <% @store.errors.full_messages.each do |message| %>
                <li><%= message %></li>
              <% end %>
            </ul>
          </div>
        </div>
      </div>
    </div>
  <% end %>

  <!-- Edit Form -->
  <div class="bg-white shadow-sm rounded-lg border border-gray-200">
    <div class="px-6 py-5 border-b border-gray-200">
      <h3 class="text-lg leading-6 font-semibold text-gray-900">Store Information</h3>
      <p class="mt-1 text-sm text-gray-500">
        Update the store's basic information and settings.
      </p>
    </div>

    <div class="px-6 py-5">
      <%= form_with model: [:admin, @store], local: true, class: "space-y-6" do |f| %>

        <!-- Store Name -->
        <div>
          <%= f.label :name, "Store Name", class: "block text-sm font-medium text-gray-700" %>
          <%= f.text_field :name,
              class: "mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm" %>
          <p class="mt-2 text-sm text-gray-500">
            The official name of the store location.
          </p>
        </div>

        <!-- Phone Number -->
        <div>
          <%= f.label :phone_number, "Phone Number", class: "block text-sm font-medium text-gray-700" %>
          <%= f.telephone_field :phone_number,
              placeholder: "Enter phone number",
              class: "mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm" %>
          <p class="mt-2 text-sm text-gray-500">
            Contact phone number for the store.
          </p>
        </div>

        <!-- Status Selection -->
        <div>
          <%= f.label :status, "Status", class: "block text-sm font-medium text-gray-700" %>
          <%= f.select :status,
              options_for_select([
                ['Requested', 'requested'],
                ['Active', 'active'],
                ['Inactive', 'inactive']
              ], @store.status),
              {},
              { class: "mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm" } %>
          <p class="mt-2 text-sm text-gray-500">
            <span class="font-medium">Requested:</span> Pending approval.
            <span class="font-medium">Active:</span> Operational and accepting users.
            <span class="font-medium">Inactive:</span> Temporarily disabled.
          </p>
        </div>

        <!-- Brand Assignment -->
        <div>
          <%= f.label :brand_id, "Brand", class: "block text-sm font-medium text-gray-700" %>
          <%= f.collection_select :brand_id,
              Brand.order(:name),
              :id,
              :name,
              { prompt: 'Select a brand', selected: @store.brand_id },
              { class: "mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm" } %>
          <p class="mt-2 text-sm text-gray-500">
            The brand this store belongs to.
          </p>
        </div>

        <!-- Current Statistics -->
        <div class="bg-gray-50 rounded-md p-4">
          <h4 class="text-sm font-medium text-gray-900 mb-3">Current Statistics</h4>
          <dl class="grid grid-cols-1 gap-x-4 gap-y-3 sm:grid-cols-3">
            <div>
              <dt class="text-sm font-medium text-gray-500">Assigned Users</dt>
              <dd class="text-sm text-gray-900">
                <span class="font-medium text-blue-600"><%= @store.users.count %> users</span>
              </dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500">Total Sales</dt>
              <dd class="text-sm text-gray-900">
                <% total_sales = Sale.joins(:user).where(users: { store_id: @store.id }).count %>
                <span class="font-medium text-green-600"><%= total_sales %> sales</span>
              </dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500">Created</dt>
              <dd class="text-sm text-gray-900">
                <%= @store.created_at.strftime('%B %d, %Y') %>
              </dd>
            </div>
          </dl>
        </div>

        <!-- Form Actions -->
        <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
          <%= link_to admin_store_path(@store),
              class: "inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" do %>
            Cancel
          <% end %>

          <%= f.submit "Update Store",
              class: "inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" %>
        </div>
      <% end %>
    </div>
  </div>

  <!-- Address Information -->
  <% if @store.address %>
    <div class="bg-white shadow-sm rounded-lg border border-gray-200">
      <div class="px-6 py-5 border-b border-gray-200">
        <h3 class="text-lg leading-6 font-semibold text-gray-900">Address Information</h3>
        <p class="mt-1 text-sm text-gray-500">
          Current address on file for this store.
        </p>
      </div>

      <div class="px-6 py-5">
        <dl class="grid grid-cols-1 gap-x-4 gap-y-4 sm:grid-cols-2">
          <div>
            <dt class="text-sm font-medium text-gray-500">Street Address</dt>
            <dd class="mt-1 text-sm text-gray-900"><%= @store.address.street %></dd>
          </div>
          <div>
            <dt class="text-sm font-medium text-gray-500">City</dt>
            <dd class="mt-1 text-sm text-gray-900"><%= @store.address.city %></dd>
          </div>
          <div>
            <dt class="text-sm font-medium text-gray-500">State</dt>
            <dd class="mt-1 text-sm text-gray-900"><%= @store.address.state&.name %></dd>
          </div>
          <div>
            <dt class="text-sm font-medium text-gray-500">Postal Code</dt>
            <dd class="mt-1 text-sm text-gray-900"><%= @store.address.postal_code %></dd>
          </div>
          <div>
            <dt class="text-sm font-medium text-gray-500">Country</dt>
            <dd class="mt-1 text-sm text-gray-900"><%= @store.address.country&.name %></dd>
          </div>
        </dl>

        <div class="mt-4 pt-4 border-t border-gray-200">
          <p class="text-sm text-gray-500">
            <strong>Note:</strong> Address information is set when the store is created and cannot be modified through this interface.
            Contact system administrators if address changes are needed.
          </p>
        </div>
      </div>
    </div>
  <% end %>

  <!-- Danger Zone -->
  <div class="bg-white shadow-sm rounded-lg border border-red-200">
    <div class="px-6 py-5 border-b border-red-200">
      <h3 class="text-lg leading-6 font-semibold text-red-900">Store Actions</h3>
      <p class="mt-1 text-sm text-red-600">
        Administrative actions for this store.
      </p>
    </div>

    <div class="px-6 py-5">
      <div class="space-y-4">
        <!-- Approve Store (if requested) -->
        <% if @store.requested? %>
          <div class="flex items-center justify-between">
            <div>
              <h4 class="text-sm font-medium text-gray-900">Approve Store</h4>
              <p class="text-sm text-gray-500">
                Approve this store request and make it active for user assignments.
              </p>
            </div>
            <div>
              <%= button_to approve_admin_store_path(@store),
                  method: :post,
                  class: "inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500",
                  data: { confirm: "Are you sure you want to approve this store? It will become active and available for user assignments." } do %>
                Approve Store
              <% end %>
            </div>
          </div>
        <% end %>

        <!-- Activate/Deactivate Store -->
        <div class="flex items-center justify-between">
          <div>
            <h4 class="text-sm font-medium text-gray-900">
              <% if @store.active? %>
                Deactivate Store
              <% else %>
                Activate Store
              <% end %>
            </h4>
            <p class="text-sm text-gray-500">
              <% if @store.active? %>
                Temporarily disable this store. Users will not be able to record sales.
              <% else %>
                Reactivate this store for normal operations.
              <% end %>
            </p>
          </div>
          <div>
            <% if @store.active? %>
              <%= button_to deactivate_admin_store_path(@store),
                  method: :post,
                  class: "inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-yellow-600 hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500",
                  data: { confirm: "Are you sure you want to deactivate this store? Users will not be able to record sales for this location." } do %>
                Deactivate Store
              <% end %>
            <% elsif @store.inactive? %>
              <%= button_to activate_admin_store_path(@store),
                  method: :post,
                  class: "inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500",
                  data: { confirm: "Are you sure you want to activate this store?" } do %>
                Activate Store
              <% end %>
            <% end %>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>