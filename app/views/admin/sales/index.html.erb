<!-- Admin Sales Management -->
<div class="space-y-6">
  <!-- <PERSON> Header -->
  <div class="md:flex md:items-center md:justify-between">
    <div class="flex-1 min-w-0">
      <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
        Sales Management
      </h2>
      <p class="mt-1 text-sm text-gray-500">
        Review and approve sales submissions
      </p>
    </div>
  </div>

  <!-- Search and Filters -->
  <div class="bg-white shadow rounded-lg p-6">
    <%= search_form_for @q, url: admin_sales_path, method: :get, 
        html: { class: "space-y-4" } do |f| %>
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <!-- Status Filter -->
        <div>
          <%= f.label :status_eq, "Status", class: "block text-sm font-medium text-gray-700" %>
          <%= f.select :status_eq, 
              options_for_select([
                ['All Statuses', ''],
                ['Pending', 'pending'],
                ['Approved', 'approved'],
                ['Rejected', 'rejected']
              ], params.dig(:q, :status_eq)),
              {},
              { class: "mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-zeiss-500 focus:ring-zeiss-500" } %>
        </div>

        <!-- User Email Search -->
        <div>
          <%= f.label :user_email_cont, "User Email", class: "block text-sm font-medium text-gray-700" %>
          <%= f.text_field :user_email_cont, 
              placeholder: "Search by email",
              class: "mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-zeiss-500 focus:ring-zeiss-500" %>
        </div>

        <!-- Product Name Search -->
        <div>
          <%= f.label :product_name_cont, "Product", class: "block text-sm font-medium text-gray-700" %>
          <%= f.text_field :product_name_cont, 
              placeholder: "Search by product",
              class: "mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-zeiss-500 focus:ring-zeiss-500" %>
        </div>

        <!-- Serial Number Search -->
        <div>
          <%= f.label :serial_number_cont, "Serial Number", class: "block text-sm font-medium text-gray-700" %>
          <%= f.text_field :serial_number_cont, 
              placeholder: "Search by serial",
              class: "mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-zeiss-500 focus:ring-zeiss-500" %>
        </div>
      </div>

      <div class="flex justify-between items-center">
        <div class="flex space-x-3">
          <%= f.submit "Search", 
              class: "inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-zeiss-600 hover:bg-zeiss-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500" %>
          <%= link_to admin_sales_path, 
              class: "inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500" do %>
            Clear
          <% end %>
        </div>
        
        <div class="text-sm text-gray-500">
          <%= pluralize(@pagy.count, 'sale') %> found
        </div>
      </div>
    <% end %>
  </div>

  <!-- Sales Table -->
  <div class="bg-white shadow rounded-lg overflow-hidden">
    <div class="overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Sale Details
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              User
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Product
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Points
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Status
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Actions
            </th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <% @sales.each do |sale| %>
            <tr class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">
                  <div class="font-medium"><%= sale.serial_number %></div>
                  <div class="text-gray-500">
                    Sold: <%= sale.sold_at.strftime('%b %d, %Y') %>
                  </div>
                  <div class="text-gray-500">
                    Submitted: <%= sale.created_at.strftime('%b %d, %Y at %I:%M %p') %>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">
                  <div class="font-medium"><%= sale.user.email %></div>
                  <div class="text-gray-500"><%= sale.user.store&.name %></div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">
                  <div class="font-medium"><%= sale.product.name %></div>
                  <div class="text-gray-500"><%= sale.product.category&.name %></div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-zeiss-600">
                <%= sale.points %> pts
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                             <%= sale.status == 'approved' ? 'bg-green-100 text-green-800' : 
                                 sale.status == 'pending' ? 'bg-yellow-100 text-yellow-800' : 
                                 'bg-red-100 text-red-800' %>">
                  <%= sale.status.capitalize %>
                </span>
                <% if sale.approved_by_admin %>
                  <div class="text-xs text-gray-500 mt-1">
                    by <%= sale.approved_by_admin.email %>
                  </div>
                  <div class="text-xs text-gray-500">
                    <%= sale.approved_at&.strftime('%b %d, %Y') %>
                  </div>
                <% end %>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                <%= link_to admin_sale_path(sale), 
                    class: "text-zeiss-600 hover:text-zeiss-900" do %>
                  View
                <% end %>
                
                <% if sale.pending? %>
                  <%= button_to approve_admin_sale_path(sale), 
                      method: :post,
                      class: "inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500",
                      data: { confirm: "Are you sure you want to approve this sale?" } do %>
                    Approve
                  <% end %>
                  
                  <%= button_to reject_admin_sale_path(sale), 
                      method: :post,
                      class: "inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500",
                      data: { confirm: "Are you sure you want to reject this sale?" } do %>
                    Reject
                  <% end %>
                <% end %>
              </td>
            </tr>
          <% end %>
        </tbody>
      </table>
    </div>

    <!-- Pagination -->
    <% if @pagy.pages > 1 %>
      <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
        <div class="flex-1 flex justify-between sm:hidden">
          <% if @pagy.prev %>
            <%= link_to "Previous", admin_sales_path(page: @pagy.prev), 
                class: "relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50" %>
          <% else %>
            <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-400 bg-gray-50 pointer-events-none">Previous</span>
          <% end %>
          
          <% if @pagy.next %>
            <%= link_to "Next", admin_sales_path(page: @pagy.next), 
                class: "ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50" %>
          <% else %>
            <span class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-400 bg-gray-50 pointer-events-none">Next</span>
          <% end %>
        </div>
        
        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
          <div>
            <p class="text-sm text-gray-700">
              Showing
              <span class="font-medium"><%= @pagy.from %></span>
              to
              <span class="font-medium"><%= @pagy.to %></span>
              of
              <span class="font-medium"><%= @pagy.count %></span>
              results
            </p>
          </div>
          <div>
            <%== pagy_nav(@pagy, link_extra: 'class="relative inline-flex items-center px-3 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"') %>
          </div>
        </div>
      </div>
    <% end %>
  </div>

  <!-- Empty State -->
  <% if @sales.empty? %>
    <div class="bg-white shadow rounded-lg p-6">
      <div class="text-center">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">No sales found</h3>
        <p class="mt-1 text-sm text-gray-500">
          No sales match your current search criteria.
        </p>
      </div>
    </div>
  <% end %>
</div>