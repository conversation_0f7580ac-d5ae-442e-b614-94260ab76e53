<% # Pick a random background image from the account images %>
<% images = [
  asset_path('account/zeiss_background_1.webp'),
  asset_path('account/zeiss_background_2.webp'),
  asset_path('account/zeiss_background_3.webp')
] %>
<% bg_image = images.sample %>

<!-- Mobile-first PWA Store Search Page -->
<div class="min-h-screen w-full flex flex-col relative overflow-hidden"
     style="background-image: url('<%= bg_image %>'); background-size: cover; background-position: center; background-repeat: no-repeat;">

  <!-- Background overlay for better readability -->
  <div class="absolute inset-0 bg-black/30"></div>

  <!-- Main content container -->
  <div class="relative z-10 flex flex-col min-h-screen">

    <!-- Header section -->
    <header class="flex-shrink-0 pt-8 pb-4 px-4 sm:pt-12 sm:pb-6">
      <div class="text-center">
        <%= image_tag 'zeisslogo.png', alt: '<PERSON>eiss Logo',
            class: 'h-12 w-auto mx-auto mb-4 sm:h-16 drop-shadow-lg' %>
        <h1 class="text-2xl sm:text-3xl font-bold text-white mb-2 drop-shadow-lg">
          Find Your Store
        </h1>
        <p class="text-base sm:text-lg text-white/90 font-medium drop-shadow">
          Step 1 of 3: Select your Zeiss store
        </p>
      </div>
    </header>

    <!-- Main content area -->
    <main class="flex-1 px-4 py-6">
      <div class="max-w-lg mx-auto space-y-6">

        <!-- Store Search Card -->
        <div class="bg-white/95 backdrop-blur-sm rounded-2xl shadow-2xl p-6 sm:p-8">

          <!-- Search Form -->
          <%= search_form_for @q, url: search_stores_path, method: :get,
              html: { class: 'space-y-5' } do |f| %>

            <div class="space-y-2">
              <%= f.label :name_cont, 'Search for your store',
                  class: 'block text-lg font-bold text-gray-900' %>
              <div class="relative">
                <%= f.search_field :name_cont,
                    value: @query,
                    placeholder: 'Enter store name or location',
                    class: 'w-full px-4 py-3 pr-12 rounded-xl border border-gray-300
                           focus:ring-2 focus:ring-zeiss-500 focus:border-zeiss-500
                           transition-colors text-base sm:text-sm
                           placeholder:text-gray-400' %>
                <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                  <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                  </svg>
                </div>
              </div>
            </div>

            <%= f.submit 'Search Stores',
                class: 'w-full py-3 px-4 bg-zeiss-600 hover:bg-zeiss-700
                       text-white font-semibold rounded-xl
                       transition-colors duration-200
                       focus:ring-2 focus:ring-zeiss-500 focus:ring-offset-2
                       text-base sm:text-sm' %>
          <% end %>
        </div>

        <!-- Search Results - Only show after search -->
        <% if @query.present? %>
          <% if defined?(@stores) && @stores.any? %>
            <div class="bg-white/95 backdrop-blur-sm rounded-2xl shadow-2xl p-6 sm:p-8">
              <h2 class="text-xl font-bold text-gray-900 mb-4">Select Your Store</h2>
              <div class="space-y-3">
                <% @stores.each do |store| %>
                  <div class="flex items-center justify-between p-4 bg-gray-50 rounded-xl border border-gray-200 hover:bg-gray-100 transition-colors">
                    <div class="flex-1">
                      <h3 class="font-semibold text-gray-900"><%= store.name %></h3>
                      <% if store.address %>
                        <p class="text-sm text-gray-600 mt-1">
                          <%= store.address.city %>, <%= store.address.state&.name %>
                        </p>
                      <% end %>
                    </div>
                    <%= button_to 'Select', select_store_path(store),
                        method: :post,
                        class: 'ml-4 py-2 px-4 bg-zeiss-600 hover:bg-zeiss-700
                               text-white font-medium rounded-lg
                               transition-colors duration-200 text-sm' %>
                  </div>
                <% end %>
              </div>
            </div>
          <% else %>
            <div class="bg-white/95 backdrop-blur-sm rounded-2xl shadow-2xl p-6 sm:p-8">
              <div class="text-center">
                <div class="w-16 h-16 mx-auto mb-4 bg-red-100 rounded-full flex items-center justify-center">
                  <svg class="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                  </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">No stores found</h3>
                <p class="text-gray-600">No stores found for '<%= @query %>'. Try a different search or add your store below.</p>
              </div>
            </div>
          <% end %>

          <!-- Add New Store Section - Only show after search -->
          <div class="bg-white/95 backdrop-blur-sm rounded-2xl shadow-2xl p-6 sm:p-8">
            <div class="text-center mb-6">
              <div class="w-12 h-12 mx-auto mb-3 bg-zeiss-100 rounded-full flex items-center justify-center">
                <svg class="w-6 h-6 text-zeiss-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
              </div>
              <h3 class="text-lg font-bold text-gray-900 mb-2">Can't find your store?</h3>
              <p class="text-gray-600 text-sm">Add your store details and we'll review it for approval</p>
            </div>

            <%= form_with url: create_store_path, method: :post,
              data: { turbo_frame: 'new_store' },
              class: "space-y-5" do |f| %>

              <!-- Store Name -->
              <div class="space-y-2">
                <%= f.label :name, 'Store Name',
                  class: 'block text-sm font-semibold text-gray-700' %>
                <%= f.text_field :name,
                  name: 'store[name]',
                  required: true,
                  placeholder: 'Enter store name',
                  class: 'w-full px-4 py-3 rounded-xl border border-gray-300
                         focus:ring-2 focus:ring-zeiss-500 focus:border-zeiss-500
                         transition-colors text-base sm:text-sm
                         placeholder:text-gray-400' %>
              </div>

              <!-- Phone Number -->
              <div class="space-y-2">
                <%= f.label :phone_number, 'Phone Number',
                  class: 'block text-sm font-semibold text-gray-700' %>
                <%= f.telephone_field :phone_number,
                  name: 'store[phone_number]',
                  required: true,
                  placeholder: 'Enter phone number',
                  class: 'w-full px-4 py-3 rounded-xl border border-gray-300
                         focus:ring-2 focus:ring-zeiss-500 focus:border-zeiss-500
                         transition-colors text-base sm:text-sm
                         placeholder:text-gray-400' %>
              </div>

              <!-- Address Section -->
              <fieldset class="space-y-4">
                <legend class="text-sm font-semibold text-gray-700 mb-3">Store Address</legend>

                <!-- Street -->
                <div class="space-y-2">
                  <%= f.label :street, 'Street Address',
                    class: 'block text-sm font-medium text-gray-700' %>
                  <%= f.text_field :street,
                    name: 'address[street]',
                    required: true,
                    placeholder: 'Enter street address',
                    class: 'w-full px-4 py-3 rounded-xl border border-gray-300
                           focus:ring-2 focus:ring-zeiss-500 focus:border-zeiss-500
                           transition-colors text-base sm:text-sm
                           placeholder:text-gray-400' %>
                </div>

                <!-- City and Postal Code -->
                <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div class="space-y-2">
                    <%= f.label :city, 'City',
                      class: 'block text-sm font-medium text-gray-700' %>
                    <%= f.text_field :city,
                      name: 'address[city]',
                      required: true,
                      placeholder: 'Enter city',
                      class: 'w-full px-4 py-3 rounded-xl border border-gray-300
                             focus:ring-2 focus:ring-zeiss-500 focus:border-zeiss-500
                             transition-colors text-base sm:text-sm
                             placeholder:text-gray-400' %>
                  </div>
                  <div class="space-y-2">
                    <%= f.label :postal_code, 'Postal Code',
                      class: 'block text-sm font-medium text-gray-700' %>
                    <%= f.text_field :postal_code,
                      name: 'address[postal_code]',
                      required: true,
                      placeholder: 'Enter postal code',
                      class: 'w-full px-4 py-3 rounded-xl border border-gray-300
                             focus:ring-2 focus:ring-zeiss-500 focus:border-zeiss-500
                             transition-colors text-base sm:text-sm
                             placeholder:text-gray-400' %>
                  </div>
                </div>

                <!-- State and Country -->
                <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div class="space-y-2">
                    <%= f.label :state_id, 'State',
                      class: 'block text-sm font-medium text-gray-700' %>
                    <%= f.collection_select :state_id, State.order(:name), :id, :name,
                      {prompt: 'Select a state'},
                      {name: 'address[state_id]',
                       required: true,
                       class: 'w-full px-4 py-3 rounded-xl border border-gray-300
                              focus:ring-2 focus:ring-zeiss-500 focus:border-zeiss-500
                              transition-colors text-base sm:text-sm'} %>
                  </div>
                  <div class="space-y-2">
                    <%= f.label :country_id, 'Country',
                      class: 'block text-sm font-medium text-gray-700' %>
                    <%= f.collection_select :country_id, Country.order(:name), :id, :name,
                      {prompt: 'Select a country'},
                      {name: 'address[country_id]',
                       required: true,
                       class: 'w-full px-4 py-3 rounded-xl border border-gray-300
                              focus:ring-2 focus:ring-zeiss-500 focus:border-zeiss-500
                              transition-colors text-base sm:text-sm'} %>
                  </div>
                </div>
              </fieldset>

              <!-- Submit Button -->
              <div class="pt-2">
                <%= f.submit 'Request Store Addition',
                  class: 'w-full py-3 px-4 bg-green-600 hover:bg-green-700
                         text-white font-semibold rounded-xl
                         transition-colors duration-200
                         focus:ring-2 focus:ring-green-500 focus:ring-offset-2
                         text-base sm:text-sm' %>
              </div>
            <% end %>
          </div>
        <% end %>

        <!-- Navigation -->
        <div class="text-center">
          <%= link_to 'Back to Sign In', root_path,
              class: 'inline-flex items-center text-white/90 hover:text-white
                     font-medium transition-colors' %>
        </div>
      </div>
    </main>
  </div>
</div>
