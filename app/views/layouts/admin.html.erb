<!DOCTYPE html>
<html>
  <head>
    <title>ZeissPoints Admin</title>
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>

    <%= stylesheet_link_tag "application", "data-turbo-track": "reload" %>
    <%= stylesheet_link_tag "tailwind", "data-turbo-track": "reload" %>
    <%= javascript_importmap_tags %>
  </head>

  <body class="bg-gray-100">
    <!-- Admin Top Navigation -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex items-center">
            <%= image_tag 'zeisslogo.png', alt: 'Zeiss Logo', class: 'h-8 w-auto mr-3' %>
            <div>
              <h1 class="text-xl font-bold text-gray-900">ZeissPoints Admin</h1>
              <p class="text-xs text-gray-500">Administrative Dashboard</p>
            </div>
          </div>

          <div class="flex items-center space-x-4">
            <!-- Admin User Info -->
            <div class="flex items-center space-x-2">
              <div class="bg-red-100 px-3 py-1 rounded-full">
                <span class="text-sm font-semibold text-red-700">
                  <%= current_user.role.humanize %>
                </span>
              </div>
              <span class="text-sm text-gray-700"><%= current_user.email %></span>
            </div>

            <!-- Back to App -->
            <%= link_to root_path, class: "inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50" do %>
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
              </svg>
              Back to App
            <% end %>

            <!-- Sign Out -->
            <%= link_to destroy_user_session_path, method: :delete,
                class: "inline-flex items-center px-3 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-red-600 hover:bg-red-700" do %>
              Sign Out
            <% end %>
          </div>
        </div>
      </div>
    </nav>

    <div class="flex">
      <!-- Admin Sidebar -->
      <nav class="w-64 bg-white shadow-sm min-h-screen">
        <div class="p-4">
          <ul class="space-y-2">
            <!-- Dashboard -->
            <li>
              <%= link_to admin_root_path,
                  class: "flex items-center px-4 py-2 text-sm font-medium rounded-md #{request.path == admin_root_path ? 'bg-zeiss-100 text-zeiss-700' : 'text-gray-600 hover:bg-gray-50'}" do %>
                <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z"></path>
                </svg>
                Dashboard
              <% end %>
            </li>

            <!-- Sales Management -->
            <li>
              <%= link_to admin_sales_path,
                  class: "flex items-center px-4 py-2 text-sm font-medium rounded-md #{request.path.start_with?('/admin/sales') ? 'bg-zeiss-100 text-zeiss-700' : 'text-gray-600 hover:bg-gray-50'}" do %>
                <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                </svg>
                Sales
                <% if (pending_count = Sale.pending.count) > 0 %>
                  <span class="ml-auto bg-red-100 text-red-800 text-xs font-medium px-2 py-1 rounded-full">
                    <%= pending_count %>
                  </span>
                <% end %>
              <% end %>
            </li>

            <!-- Users Management -->
            <li>
              <%= link_to admin_users_path,
                  class: "flex items-center px-4 py-2 text-sm font-medium rounded-md #{request.path.start_with?('/admin/users') ? 'bg-zeiss-100 text-zeiss-700' : 'text-gray-600 hover:bg-gray-50'}" do %>
                <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                </svg>
                Users
              <% end %>
            </li>

            <!-- Stores Management -->
            <li>
              <%= link_to admin_stores_path,
                  class: "flex items-center px-4 py-2 text-sm font-medium rounded-md #{request.path.start_with?('/admin/stores') ? 'bg-zeiss-100 text-zeiss-700' : 'text-gray-600 hover:bg-gray-50'}" do %>
                <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                </svg>
                Stores
                <% if (requested_count = Store.requested.count) > 0 %>
                  <span class="ml-auto bg-yellow-100 text-yellow-800 text-xs font-medium px-2 py-1 rounded-full">
                    <%= requested_count %>
                  </span>
                <% end %>
              <% end %>
            </li>
          </ul>
        </div>
      </nav>

      <!-- Main Content -->
      <main class="flex-1 p-6">
        <!-- Flash Messages -->
        <% if notice %>
          <div class="mb-4 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-md">
            <%= notice %>
          </div>
        <% end %>

        <% if alert %>
          <div class="mb-4 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
            <%= alert %>
          </div>
        <% end %>

        <%= yield %>
      </main>
    </div>
  </body>
</html>