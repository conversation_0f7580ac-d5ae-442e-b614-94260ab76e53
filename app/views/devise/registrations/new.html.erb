<% # Pick a random background image from the account images %>
<% images = [
  asset_path('account/zeiss_background_1.webp'),
  asset_path('account/zeiss_background_2.webp'),
  asset_path('account/zeiss_background_3.webp')
] %>
<% bg_image = images.sample %>

<!-- Mobile-first PWA Registration Page -->
<div class="min-h-screen w-full flex flex-col relative overflow-hidden"
     style="background-image: url('<%= bg_image %>'); background-size: cover; background-position: center; background-repeat: no-repeat;">

  <!-- Background overlay for better readability -->
  <div class="absolute inset-0 bg-black/30"></div>

  <!-- Main content container -->
  <div class="relative z-10 flex flex-col min-h-screen">

    <!-- Header section -->
    <header class="flex-shrink-0 pt-8 pb-4 px-4 sm:pt-12 sm:pb-6">
      <div class="text-center">
        <%= image_tag 'zeisslogo.png', alt: '<PERSON>eiss Logo',
            class: 'h-12 w-auto mx-auto mb-4 sm:h-16 drop-shadow-lg' %>
        <h1 class="text-2xl sm:text-3xl font-bold text-white mb-2 drop-shadow-lg">
          Create Your Account
        </h1>
        <p class="text-base sm:text-lg text-white/90 font-medium drop-shadow">
          Step 2 of 3: Complete your registration
        </p>
      </div>
    </header>

    <!-- Main content area -->
    <main class="flex-1 px-4 py-6">
      <div class="max-w-lg mx-auto space-y-6">

        <!-- Selected Store Display -->
        <% if session[:selected_store_id] && (store = Store.find_by(id: session[:selected_store_id])) %>
          <div class="bg-green-50 border border-green-200 rounded-2xl p-4">
            <div class="flex items-center">
              <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-3">
                <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
              </div>
              <div>
                <p class="text-sm font-medium text-green-800">Selected Store</p>
                <p class="text-green-700 font-semibold"><%= store.name %></p>
              </div>
            </div>
          </div>
        <% end %>

        <!-- Registration Form Card -->
        <div class="bg-white/95 backdrop-blur-sm rounded-2xl shadow-2xl p-6 sm:p-8">

          <!-- Error Messages -->
          <% if resource.errors.any? %>
            <div class="mb-6 p-4 bg-red-50 border border-red-200 rounded-xl">
              <div class="flex items-center mb-2">
                <svg class="w-5 h-5 text-red-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
                <h3 class="text-sm font-medium text-red-800">Please fix the following errors:</h3>
              </div>
              <ul class="text-sm text-red-700 space-y-1">
                <% resource.errors.full_messages.each do |message| %>
                  <li>&bull; <%= message %></li>
                <% end %>
              </ul>
            </div>
          <% end %>

          <%= form_for(resource, as: resource_name, url: registration_path(resource_name),
              html: { class: "space-y-6" }) do |f| %>

            <%= hidden_field_tag :store_id, session[:selected_store_id] if session[:selected_store_id] %>

            <!-- Email Section -->
            <div class="space-y-2">
              <%= f.label :email, "Email Address",
                  class: "block text-sm font-semibold text-gray-700" %>
              <%= f.email_field :email,
                  autofocus: true,
                  autocomplete: "email",
                  placeholder: "Enter your email address",
                  class: "w-full px-4 py-3 rounded-xl border border-gray-300
                         focus:ring-2 focus:ring-zeiss-500 focus:border-zeiss-500
                         transition-colors text-base sm:text-sm
                         placeholder:text-gray-400" %>
            </div>

            <!-- Address Section -->
            <fieldset class="space-y-4">
              <legend class="text-sm font-semibold text-gray-700 mb-3">Your Address</legend>

              <%= f.fields_for :address, resource.address || resource.build_address do |address_form| %>
                <!-- Street Address -->
                <div class="space-y-2">
                  <%= address_form.label :street, "Street Address",
                      class: "block text-sm font-medium text-gray-700" %>
                  <%= address_form.text_field :street,
                      placeholder: "Enter your street address",
                      class: "w-full px-4 py-3 rounded-xl border border-gray-300
                             focus:ring-2 focus:ring-zeiss-500 focus:border-zeiss-500
                             transition-colors text-base sm:text-sm
                             placeholder:text-gray-400" %>
                </div>

                <!-- City and Postal Code -->
                <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div class="space-y-2">
                    <%= address_form.label :city, "City",
                        class: "block text-sm font-medium text-gray-700" %>
                    <%= address_form.text_field :city,
                        placeholder: "Enter your city",
                        class: "w-full px-4 py-3 rounded-xl border border-gray-300
                               focus:ring-2 focus:ring-zeiss-500 focus:border-zeiss-500
                               transition-colors text-base sm:text-sm
                               placeholder:text-gray-400" %>
                  </div>
                  <div class="space-y-2">
                    <%= address_form.label :postal_code, "Postal Code",
                        class: "block text-sm font-medium text-gray-700" %>
                    <%= address_form.text_field :postal_code,
                        placeholder: "Enter postal code",
                        class: "w-full px-4 py-3 rounded-xl border border-gray-300
                               focus:ring-2 focus:ring-zeiss-500 focus:border-zeiss-500
                               transition-colors text-base sm:text-sm
                               placeholder:text-gray-400" %>
                  </div>
                </div>

                <!-- State and Country -->
                <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div class="space-y-2">
                    <%= address_form.label :state_id, "State",
                        class: "block text-sm font-medium text-gray-700" %>
                    <%= address_form.collection_select :state_id, State.order(:name), :id, :name,
                        {prompt: 'Select your state'},
                        {class: "w-full px-4 py-3 rounded-xl border border-gray-300
                                focus:ring-2 focus:ring-zeiss-500 focus:border-zeiss-500
                                transition-colors text-base sm:text-sm"} %>
                  </div>
                  <div class="space-y-2">
                    <%= address_form.label :country_id, "Country",
                        class: "block text-sm font-medium text-gray-700" %>
                    <%= address_form.collection_select :country_id, Country.order(:name), :id, :name,
                        {prompt: 'Select your country'},
                        {class: "w-full px-4 py-3 rounded-xl border border-gray-300
                                focus:ring-2 focus:ring-zeiss-500 focus:border-zeiss-500
                                transition-colors text-base sm:text-sm"} %>
                  </div>
                </div>
              <% end %>
            </fieldset>

            <!-- Password Section -->
            <div class="space-y-4">
              <div class="space-y-2">
                <%= f.label :password, "Password",
                    class: "block text-sm font-semibold text-gray-700" %>
                <% if @minimum_password_length %>
                  <p class="text-xs text-gray-500 mb-2">
                    Minimum <%= @minimum_password_length %> characters required
                  </p>
                <% end %>
                <%= f.password_field :password,
                    autocomplete: "new-password",
                    placeholder: "Create a secure password",
                    class: "w-full px-4 py-3 rounded-xl border border-gray-300
                           focus:ring-2 focus:ring-zeiss-500 focus:border-zeiss-500
                           transition-colors text-base sm:text-sm
                           placeholder:text-gray-400" %>
              </div>

              <div class="space-y-2">
                <%= f.label :password_confirmation, "Confirm Password",
                    class: "block text-sm font-semibold text-gray-700" %>
                <%= f.password_field :password_confirmation,
                    autocomplete: "new-password",
                    placeholder: "Confirm your password",
                    class: "w-full px-4 py-3 rounded-xl border border-gray-300
                           focus:ring-2 focus:ring-zeiss-500 focus:border-zeiss-500
                           transition-colors text-base sm:text-sm
                           placeholder:text-gray-400" %>
              </div>
            </div>

            <!-- Submit Button -->
            <div class="pt-4">
              <%= f.submit "Create Account",
                  class: "w-full py-3 px-4 bg-zeiss-600 hover:bg-zeiss-700
                         text-white font-semibold rounded-xl
                         transition-colors duration-200
                         focus:ring-2 focus:ring-zeiss-500 focus:ring-offset-2
                         text-base sm:text-sm" %>
            </div>
          <% end %>

          <!-- Sign In Link -->
          <div class="mt-6 text-center">
            <p class="text-sm text-gray-600">
              Already have an account?
              <%= link_to "Sign in", new_session_path(resource_name),
                  class: "text-zeiss-600 hover:text-zeiss-700 font-medium
                         hover:underline transition-colors ml-1" %>
            </p>
          </div>
        </div>

        <!-- Navigation -->
        <div class="text-center space-y-2">
          <%= link_to 'Change Store', search_stores_path,
              class: 'inline-flex items-center text-white/90 hover:text-white
                     font-medium transition-colors' %>
        </div>
      </div>
    </main>
  </div>
</div>
