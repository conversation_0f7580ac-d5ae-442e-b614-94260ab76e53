Rails.application.routes.draw do
  # Admin namespace (requires authentication)
  authenticate :user, lambda { |u| u.admin? } do
    namespace :admin do
      root "dashboard#index"

      resources :sales do
        member do
          post :approve
          post :reject
        end
      end

      resources :users do
        member do
          post :activate
          post :deactivate
        end
      end

      resources :stores do
        member do
          post :approve
          post :activate
          post :deactivate
        end
      end
    end
  end

  # Store search and selection (authenticated)
  authenticate :user do
    root to: "dashboard#show", as: :authenticated_root

    get "stores/search", to: "stores#search", as: :search_stores
    post "stores/select/:id", to: "stores#select", as: :select_store
    post "stores/create_store", to: "stores#create_store", as: :create_store

    resources :orders, only: [:index, :new, :create]
    resource :cart, only: [:show, :destroy]
    resources :line_items, only: [:create, :update, :destroy]
    resources :sales, only: [:new, :create] do
      collection do
        get :points
      end
    end
  end

  # Devise routes (public for registration/login)
  devise_for :users, controllers: {registrations: "users/registrations"}

  # Health check (public)
  get "up", to: "rails/health#show", as: :rails_health_check

  # PWA manifest and service worker (public)
  get "manifest", to: "rails/pwa#manifest", as: :pwa_manifest
  get "service-worker", to: "rails/pwa#service_worker", as: :pwa_service_worker

  # Public root
  root "landing#index"
end
